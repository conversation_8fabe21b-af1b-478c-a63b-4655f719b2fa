#!/usr/bin/env python3
"""
Simple test to verify the Apply to Main synchronization fix.
Tests the core logic without GUI complications.
"""

import sys
import os

# Add the current directory to the path so we can import the analyzer
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_filtering_logic():
    """Test that the filtering logic produces consistent results"""
    print("🧪 Testing filtering logic synchronization...")
    
    # Mock joint data with various confidence levels
    mock_joints = [
        {'position': (100, 50), 'confidence': 0.9, 'detection_method': 'sequential'},
        {'position': (110, 60), 'confidence': 0.6, 'detection_method': 'sequential'},
        {'position': (120, 70), 'confidence': 0.8, 'detection_method': 'sequential'},
        {'position': (130, 80), 'confidence': 0.5, 'detection_method': 'sequential'},
        {'position': (140, 90), 'confidence': 0.75, 'detection_method': 'sequential'},
    ]
    
    print(f"📊 Input: {len(mock_joints)} joints")
    for joint in mock_joints:
        print(f"   Joint at {joint['position']} - confidence: {joint['confidence']:.2f}")
    
    # Test confidence filtering (same logic as in the fix)
    confidence_threshold = 0.7
    confidence_filtered = [j for j in mock_joints if j.get('confidence', 0.5) >= confidence_threshold]
    
    print(f"\n🔍 After confidence filtering (≥ {confidence_threshold:.1f}): {len(confidence_filtered)} joints")
    for joint in confidence_filtered:
        print(f"   Joint at {joint['position']} - confidence: {joint['confidence']:.2f}")
    
    # Test joint limiting
    limit_joints_enabled = True
    max_joints_per_stroke = 2
    
    if limit_joints_enabled and len(confidence_filtered) > max_joints_per_stroke:
        confidence_filtered.sort(key=lambda x: x.get('confidence', 0.5), reverse=True)
        final_joints = confidence_filtered[:max_joints_per_stroke]
        print(f"\n⚠️ After joint limiting (max {max_joints_per_stroke}): {len(final_joints)} joints")
    else:
        final_joints = confidence_filtered
        print(f"\n✅ No joint limiting needed: {len(final_joints)} joints")
    
    for joint in final_joints:
        print(f"   Joint at {joint['position']} - confidence: {joint['confidence']:.2f}")
    
    # Verify the filtering worked as expected
    expected_joints = 2  # Should be the 2 highest confidence joints above 0.7
    if len(final_joints) == expected_joints:
        print(f"\n✅ SUCCESS: Filtering logic works correctly!")
        print(f"   Expected {expected_joints} joints, got {len(final_joints)} joints")
        return True
    else:
        print(f"\n❌ FAILURE: Filtering logic incorrect!")
        print(f"   Expected {expected_joints} joints, got {len(final_joints)} joints")
        return False

def test_confidence_scenarios():
    """Test various confidence threshold scenarios"""
    print("\n🧪 Testing different confidence scenarios...")
    
    test_cases = [
        {
            'name': 'High confidence threshold',
            'joints': [
                {'position': (100, 50), 'confidence': 0.9},
                {'position': (110, 60), 'confidence': 0.6},
                {'position': (120, 70), 'confidence': 0.8},
            ],
            'threshold': 0.8,
            'expected': 2
        },
        {
            'name': 'Low confidence threshold',
            'joints': [
                {'position': (100, 50), 'confidence': 0.9},
                {'position': (110, 60), 'confidence': 0.6},
                {'position': (120, 70), 'confidence': 0.8},
            ],
            'threshold': 0.5,
            'expected': 3
        },
        {
            'name': 'No joints pass threshold',
            'joints': [
                {'position': (100, 50), 'confidence': 0.4},
                {'position': (110, 60), 'confidence': 0.3},
                {'position': (120, 70), 'confidence': 0.2},
            ],
            'threshold': 0.7,
            'expected': 0
        }
    ]
    
    all_passed = True
    
    for test_case in test_cases:
        print(f"\n📋 Test: {test_case['name']}")
        joints = test_case['joints']
        threshold = test_case['threshold']
        expected = test_case['expected']
        
        filtered = [j for j in joints if j.get('confidence', 0.5) >= threshold]
        actual = len(filtered)
        
        if actual == expected:
            print(f"   ✅ PASS: Expected {expected}, got {actual}")
        else:
            print(f"   ❌ FAIL: Expected {expected}, got {actual}")
            all_passed = False
    
    return all_passed

if __name__ == "__main__":
    try:
        print("🔬 Testing Apply to Main synchronization fix...")
        
        test1_passed = test_filtering_logic()
        test2_passed = test_confidence_scenarios()
        
        if test1_passed and test2_passed:
            print("\n🎉 All synchronization tests passed!")
            print("✅ The Apply to Main button should now produce identical results")
            print("   between the comparison preview and main analyzer.")
            sys.exit(0)
        else:
            print("\n💥 Some tests failed!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
