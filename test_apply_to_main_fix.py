#!/usr/bin/env python3
"""
Test script to verify the "Apply to Main" button synchronization fix.
This script tests that the comparison window preview matches the main analyzer results.
"""

import tkinter as tk
import time
import sys
import os

# Add the current directory to the path so we can import the analyzer
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from stick_figure_analyzer import StickFigureAnalyzer

def test_apply_to_main_synchronization():
    """Test that Apply to Main produces identical results to comparison preview"""
    print("🧪 Testing Apply to Main synchronization fix...")
    
    # Create the application
    app = StickFigureAnalyzer()
    root = app.root
    
    # Create test strokes (simple stick figure)
    test_strokes = [
        # Head (circle-like)
        [(100, 50), (110, 45), (120, 50), (125, 60), (120, 70), (110, 75), (100, 70), (95, 60), (100, 50)],
        # Body (vertical line)
        [(112, 75), (112, 150)],
        # Left arm
        [(112, 100), (80, 120)],
        # Right arm  
        [(112, 100), (144, 120)],
        # Left leg
        [(112, 150), (90, 200)],
        # Right leg
        [(112, 150), (134, 200)]
    ]
    
    # Add test strokes to the analyzer
    app.all_strokes = test_strokes
    print(f"✅ Added {len(test_strokes)} test strokes")
    
    # Update the display
    root.update()
    
    # Open comparison window
    app.open_joint_comparison()
    print("✅ Opened comparison window")
    
    # Wait for initialization
    root.update()
    time.sleep(0.1)
    
    # Set up a specific pipeline configuration for testing
    app.selected_base_method.set('sequential')
    app.enabled_modifiers['clustering'].set(True)
    print("✅ Set pipeline: sequential + clustering")
    
    # Run the pipeline comparison
    app.run_pipeline_comparison()
    print("✅ Ran pipeline comparison")
    
    # Get comparison results
    if hasattr(app, 'final_pipeline_results') and app.final_pipeline_results:
        comparison_joint_count = len(app.final_pipeline_results['joints'])
        comparison_confidence_threshold = app.final_pipeline_results['confidence_threshold']
        comparison_limiting_enabled = app.final_pipeline_results['joint_limiting_enabled']
        print(f"📊 Comparison preview: {comparison_joint_count} joints (confidence ≥ {comparison_confidence_threshold:.1f}, limiting: {comparison_limiting_enabled})")
    else:
        print("❌ No final pipeline results found in comparison")
        return False
    
    # Apply to main analyzer
    app.apply_pipeline_to_main()
    print("✅ Applied pipeline to main analyzer")
    
    # Re-analyze strokes in main analyzer to get results
    app.analyze_strokes()
    print("✅ Re-analyzed strokes in main analyzer")
    
    # Count joints in main analyzer results
    main_joint_count = 0
    for stroke_id, stroke_data in app.body_parts.items():
        main_joint_count += len(stroke_data['joints'])
    
    print(f"📊 Main analyzer: {main_joint_count} joints")
    
    # Compare results
    if comparison_joint_count == main_joint_count:
        print("✅ SUCCESS: Joint counts match between comparison preview and main analyzer!")
        print(f"   Both show {comparison_joint_count} joints")
        success = True
    else:
        print("❌ FAILURE: Joint counts don't match!")
        print(f"   Comparison preview: {comparison_joint_count} joints")
        print(f"   Main analyzer: {main_joint_count} joints")
        success = False
    
    # Test with different settings
    print("\n🧪 Testing with joint limiting enabled...")
    
    # Enable joint limiting
    app.limit_joints_enabled = True
    app.max_joints_per_stroke = 2
    print(f"✅ Enabled joint limiting (max {app.max_joints_per_stroke} per stroke)")
    
    # Re-run comparison
    app.run_pipeline_comparison()
    comparison_joint_count_limited = len(app.final_pipeline_results['joints'])
    print(f"📊 Comparison preview (limited): {comparison_joint_count_limited} joints")
    
    # Apply to main and re-analyze
    app.apply_pipeline_to_main()
    app.analyze_strokes()
    
    main_joint_count_limited = 0
    for stroke_id, stroke_data in app.body_parts.items():
        main_joint_count_limited += len(stroke_data['joints'])
    
    print(f"📊 Main analyzer (limited): {main_joint_count_limited} joints")
    
    if comparison_joint_count_limited == main_joint_count_limited:
        print("✅ SUCCESS: Joint limiting test passed!")
        print(f"   Both show {comparison_joint_count_limited} joints with limiting")
    else:
        print("❌ FAILURE: Joint limiting test failed!")
        print(f"   Comparison preview: {comparison_joint_count_limited} joints")
        print(f"   Main analyzer: {main_joint_count_limited} joints")
        success = False
    
    # Cleanup
    if app.comparison_window:
        app.comparison_window.destroy()
    root.destroy()
    
    return success

if __name__ == "__main__":
    try:
        success = test_apply_to_main_synchronization()
        if success:
            print("\n🎉 All tests passed! Apply to Main synchronization is working correctly.")
            sys.exit(0)
        else:
            print("\n💥 Tests failed! Apply to Main synchronization needs more work.")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
