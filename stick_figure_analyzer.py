#!/usr/bin/env python3
"""
Stick Figure Analyzer - A tkinter application for analyzing hand-drawn stick figures
and generating skeletal structures with bone detection and joint identification.
"""

import tkinter as tk
from tkinter import ttk, messagebox, colorchooser, filedialog
import math
import numpy as np
from typing import List, Tuple, Dict, Optional
import json
import os

class StickFigureAnalyzer:
    def __init__(self):
        print("🚀 Initializing Stick Figure Analyzer...")
        
        # Main window setup
        self.root = tk.Tk()
        self.root.title("Stick Figure Analyzer")
        self.root.geometry("1200x800")
        
        # Application state
        self.dark_mode = False
        self.zoom_factor = 1.0
        self.drawing = False
        self.current_stroke = []
        self.all_strokes = []
        self.bones = []
        self.joints = []
        self.selected_bone = None
        self.mirror_origin_x = None
        self.body_parts = {}

        # Enhanced joint detection settings
        self.stroke_velocity_analysis = True  # Toggleable feature
        self.joint_detection_sensitivity = 0.7  # 0.0 to 1.0
        self.curvature_threshold = 0.02  # Lowered for better curved joint detection
        self.curvature_smoothing_window = 5  # Smoothing for curvature calculation
        self.joint_clustering_distance = 15  # pixels
        self.smoothing_window_size = 3

        # Joint limiting settings
        self.limit_joints_enabled = False  # Default: unlimited joints
        self.max_joints_per_stroke = 3  # Hardcoded maximum when limiting is enabled
        
        # Drawing settings
        self.min_stroke_length = 16
        self.stroke_color = "#000000"
        self.bone_color = "#FF0000"
        self.joint_color = "#0000FF"
        self.selected_color = "#00FF00"

        # Joint detection comparison system - refactored architecture
        self.comparison_window = None
        self.comparison_active = False

        # Base detection methods (choose one)
        self.base_method_controls = {}
        self.base_method_results = {}
        self.base_method_colors = {
            'sequential': '#FF0000',      # Red
            'angle': '#00FF00',           # Green
            'curvature': '#0000FF',       # Blue
            'velocity': '#FF8000',        # Orange
            'hybrid': '#FF0080',          # Pink
        }

        # Post-processing modifiers (can be applied to any base method)
        self.modifier_controls = {}
        self.modifier_results = {}
        self.modifier_colors = {
            'clustering': '#8000FF',      # Purple
        }

        # Current selections
        self.selected_base_method = tk.StringVar(value='sequential')
        self.enabled_modifiers = {}

        # Legacy compatibility
        self.detection_methods = {}
        self.method_results = {}
        self.method_colors = {**self.base_method_colors, **self.modifier_colors}

        # Main analyzer pipeline configuration (applied from comparison system)
        self.main_pipeline_config = None
        
        print("📝 Setting up UI components...")
        self.setup_ui()
        self.setup_bindings()
        
        print("✅ Application initialized successfully!")
    
    def setup_ui(self):
        """Set up the user interface components"""
        print("🎨 Creating UI components...")
        
        # Create menu bar
        self.create_menu_bar()
        
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Canvas frame with scrollbars
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create canvas
        self.canvas = tk.Canvas(
            canvas_frame,
            bg="white",
            width=800,
            height=600,
            scrollregion=(0, 0, 1600, 1200)
        )
        
        # Scrollbars
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        self.canvas.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)
        
        # Pack scrollbars and canvas
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Status bar
        self.status_bar = ttk.Label(main_frame, text="Ready to draw stick figure", relief=tk.SUNKEN)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        print("✅ UI components created successfully!")
    
    def create_menu_bar(self):
        """Create comprehensive menu bar with debugging options"""
        print("📋 Creating menu bar...")
        
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New", command=self.new_drawing)
        file_menu.add_command(label="Save", command=self.save_drawing)
        file_menu.add_command(label="Load", command=self.load_drawing)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Zoom In", command=self.zoom_in)
        view_menu.add_command(label="Zoom Out", command=self.zoom_out)
        view_menu.add_command(label="Reset Zoom", command=self.reset_zoom)
        view_menu.add_separator()
        view_menu.add_command(label="Toggle Dark Mode", command=self.toggle_dark_mode)
        
        # Analysis menu
        analysis_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Analysis", menu=analysis_menu)
        analysis_menu.add_command(label="Analyze Strokes", command=self.analyze_strokes)
        analysis_menu.add_command(label="Detect Body Parts", command=self.detect_body_parts)
        analysis_menu.add_command(label="Generate Skeleton", command=self.generate_skeleton)
        analysis_menu.add_separator()
        analysis_menu.add_command(label="Joint Detection Comparison", command=self.open_joint_comparison)
        analysis_menu.add_separator()
        analysis_menu.add_command(label="Clear Analysis", command=self.clear_analysis)
        
        # Body Parts menu
        body_parts_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Body Parts", menu=body_parts_menu)
        body_parts_menu.add_command(label="Show Head", command=lambda: self.highlight_body_part("head"))
        body_parts_menu.add_command(label="Show Torso", command=lambda: self.highlight_body_part("torso"))
        body_parts_menu.add_command(label="Show Left Arm", command=lambda: self.highlight_body_part("left_arm"))
        body_parts_menu.add_command(label="Show Right Arm", command=lambda: self.highlight_body_part("right_arm"))
        body_parts_menu.add_command(label="Show Left Leg", command=lambda: self.highlight_body_part("left_leg"))
        body_parts_menu.add_command(label="Show Right Leg", command=lambda: self.highlight_body_part("right_leg"))
        
        # Bones menu
        bones_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Bones", menu=bones_menu)
        bones_menu.add_command(label="Split Selected Bone", command=self.split_selected_bone)
        bones_menu.add_command(label="Mirror Limbs", command=self.mirror_limbs)
        bones_menu.add_command(label="Set Mirror Origin", command=self.set_mirror_origin)
        
        # Joint Detection menu
        joint_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Joint Detection", menu=joint_menu)
        joint_menu.add_command(label="Toggle Velocity Analysis", command=self.toggle_velocity_analysis)
        joint_menu.add_command(label="Toggle Joint Limiting", command=self.toggle_joint_limiting)
        joint_menu.add_separator()
        joint_menu.add_command(label="High Sensitivity", command=lambda: self.set_detection_sensitivity(0.9))
        joint_menu.add_command(label="Medium Sensitivity", command=lambda: self.set_detection_sensitivity(0.7))
        joint_menu.add_command(label="Low Sensitivity", command=lambda: self.set_detection_sensitivity(0.5))
        joint_menu.add_separator()
        joint_menu.add_command(label="Test Enhanced Detection", command=self.test_enhanced_detection)

        # Debug menu
        debug_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Debug", menu=debug_menu)
        debug_menu.add_command(label="Print Strokes", command=self.debug_print_strokes)
        debug_menu.add_command(label="Print Bones", command=self.debug_print_bones)
        debug_menu.add_command(label="Print Joints", command=self.debug_print_joints)
        debug_menu.add_command(label="Print Body Parts", command=self.debug_print_body_parts)
        debug_menu.add_command(label="Show Canvas Info", command=self.debug_canvas_info)
        debug_menu.add_separator()
        debug_menu.add_command(label="Debug Joint Detection", command=self.debug_joint_detection)
        
        print("✅ Menu bar created successfully!")
    
    def setup_bindings(self):
        """Set up event bindings for canvas interaction"""
        print("🔗 Setting up event bindings...")
        
        # Drawing bindings
        self.canvas.bind("<Button-1>", self.start_drawing)
        self.canvas.bind("<B1-Motion>", self.draw)
        self.canvas.bind("<ButtonRelease-1>", self.stop_drawing)
        
        # Selection bindings (Shift + Click)
        self.canvas.bind("<Shift-Button-1>", self.select_bone)
        
        # Zoom bindings
        self.canvas.bind("<MouseWheel>", self.mouse_wheel_zoom)
        self.root.bind("<Control-plus>", lambda _: self.zoom_in())
        self.root.bind("<Control-minus>", lambda _: self.zoom_out())
        self.root.bind("<Control-0>", lambda _: self.reset_zoom())
        
        print("✅ Event bindings set up successfully!")
    
    def toggle_dark_mode(self):
        """Toggle between light and dark mode"""
        print(f"🌓 Toggling dark mode (current: {self.dark_mode})")
        
        self.dark_mode = not self.dark_mode
        
        if self.dark_mode:
            # Dark mode colors
            bg_color = "#2b2b2b"
            fg_color = "#ffffff"
            canvas_bg = "#1e1e1e"
            self.stroke_color = "#ffffff"
        else:
            # Light mode colors
            bg_color = "#f0f0f0"
            fg_color = "#000000"
            canvas_bg = "#ffffff"
            self.stroke_color = "#000000"
        
        # Apply colors
        self.root.configure(bg=bg_color)
        self.canvas.configure(bg=canvas_bg)
        
        # Redraw everything with new colors
        self.redraw_all()
        
        print(f"✅ Dark mode {'enabled' if self.dark_mode else 'disabled'}")
    
    def redraw_all(self):
        """Redraw all strokes, bones, and joints with current colors"""
        print("🎨 Redrawing all elements...")
        
        self.canvas.delete("all")
        
        # Redraw strokes
        for stroke in self.all_strokes:
            self.draw_stroke(stroke)
        
        # Redraw bones and joints
        self.draw_skeleton()
        
        print("✅ All elements redrawn")

    # Drawing Methods
    def start_drawing(self, event):
        """Start drawing a new stroke"""
        if self.root.tk.call('tk', 'windowingsystem') == 'aqua':
            # macOS
            shift_pressed = event.state & 0x1
        else:
            # Windows/Linux
            shift_pressed = event.state & 0x1

        if shift_pressed:
            print("🔍 Shift+Click detected - selection mode")
            return

        print(f"✏️ Starting new stroke at ({event.x}, {event.y})")
        self.drawing = True
        self.current_stroke = [(event.x, event.y)]

    def draw(self, event):
        """Continue drawing the current stroke"""
        if not self.drawing:
            return

        x, y = event.x, event.y
        self.current_stroke.append((x, y))

        # Draw line segment
        if len(self.current_stroke) > 1:
            prev_x, prev_y = self.current_stroke[-2]
            self.canvas.create_line(
                prev_x, prev_y, x, y,
                fill=self.stroke_color,
                width=2,
                tags="stroke"
            )

    def stop_drawing(self, _):
        """Finish drawing the current stroke"""
        if not self.drawing:
            return

        print(f"🛑 Finishing stroke with {len(self.current_stroke)} points")
        self.drawing = False

        # Calculate stroke length for information
        stroke_length = self.calculate_stroke_length(self.current_stroke)
        print(f"📏 Stroke length: {stroke_length:.2f}px")

        # ALWAYS preserve all strokes - no minimum length filtering
        self.all_strokes.append(self.current_stroke.copy())
        print(f"✅ Added stroke #{len(self.all_strokes)} to collection (length: {stroke_length:.1f}px)")

        self.current_stroke = []

        # Update status with joint limiting info
        limiting_status = " [Joint Limiting ON]" if self.limit_joints_enabled else " [Unlimited Joints]"
        self.update_status(f"Drew stroke #{len(self.all_strokes)} ({stroke_length:.1f}px){limiting_status}")

    def calculate_stroke_length(self, stroke):
        """Calculate the total length of a stroke"""
        if len(stroke) < 2:
            return 0

        total_length = 0
        for i in range(1, len(stroke)):
            x1, y1 = stroke[i-1]
            x2, y2 = stroke[i]
            total_length += math.sqrt((x2-x1)**2 + (y2-y1)**2)

        return total_length

    def draw_stroke(self, stroke):
        """Draw a stroke on the canvas"""
        if len(stroke) < 2:
            return

        for i in range(1, len(stroke)):
            x1, y1 = stroke[i-1]
            x2, y2 = stroke[i]
            self.canvas.create_line(
                x1, y1, x2, y2,
                fill=self.stroke_color,
                width=2,
                tags="stroke"
            )

    # Zoom Methods
    def zoom_in(self):
        """Zoom in on the canvas"""
        print("🔍 Zooming in...")
        self.zoom_factor *= 1.2
        self.apply_zoom()

    def zoom_out(self):
        """Zoom out on the canvas"""
        print("🔍 Zooming out...")
        self.zoom_factor /= 1.2
        self.apply_zoom()

    def reset_zoom(self):
        """Reset zoom to 100%"""
        print("🔍 Resetting zoom to 100%")
        self.zoom_factor = 1.0
        self.apply_zoom()

    def apply_zoom(self):
        """Apply current zoom factor to canvas"""
        print(f"🔍 Applying zoom factor: {self.zoom_factor:.2f}")

        # Scale all canvas items
        self.canvas.scale("all", 0, 0, self.zoom_factor, self.zoom_factor)

        # Update scroll region
        bbox = self.canvas.bbox("all")
        if bbox:
            self.canvas.configure(scrollregion=bbox)

        self.update_status(f"Zoom: {self.zoom_factor*100:.0f}%")

    def mouse_wheel_zoom(self, event):
        """Handle mouse wheel zoom"""
        if event.delta > 0:
            self.zoom_in()
        else:
            self.zoom_out()

    # Stroke Analysis Methods
    def analyze_strokes(self):
        """Analyze all strokes to identify body parts and joints"""
        print("🔬 Starting stroke analysis...")

        if not self.all_strokes:
            print("❌ No strokes to analyze")
            messagebox.showwarning("Warning", "No strokes to analyze. Draw something first!")
            return

        print(f"📊 Analyzing {len(self.all_strokes)} strokes...")

        # Clear previous analysis
        self.body_parts = {}
        self.bones = []
        self.joints = []

        # Analyze each stroke
        for i, stroke in enumerate(self.all_strokes):
            print(f"🔍 Analyzing stroke {i+1}/{len(self.all_strokes)}")

            # Find angle changes (potential joints)
            joints_in_stroke = self.find_angle_changes(stroke)
            print(f"🦴 Found {len(joints_in_stroke)} potential joints in stroke {i+1}")

            # Classify body part
            body_part = self.classify_body_part(stroke)
            print(f"🏷️ Classified stroke {i+1} as: {body_part}")

            # Store analysis results
            self.body_parts[f"stroke_{i}"] = {
                'stroke': stroke,
                'body_part': body_part,
                'joints': joints_in_stroke
            }

        print("✅ Stroke analysis complete!")

        # Apply validation and refinement to improve classification accuracy
        self.validate_and_refine_classifications()

        self.update_status(f"Analyzed {len(self.all_strokes)} strokes")

    def find_angle_changes(self, stroke, angle_threshold=30):
        """Enhanced joint detection using pipeline configuration or fallback to legacy system"""
        if len(stroke) < 5:  # Need more points for enhanced analysis
            return []

        # Check if pipeline configuration is available from comparison system
        if hasattr(self, 'main_pipeline_config') and self.main_pipeline_config:
            print(f"🔬 Using pipeline configuration: {self.main_pipeline_config['base_method']}")
            return self.apply_main_pipeline_detection(stroke)
        else:
            print(f"🔬 Using legacy sequential joint detection on stroke with {len(stroke)} points")
            return self.legacy_joint_detection(stroke, angle_threshold)

    def apply_main_pipeline_detection(self, stroke):
        """Apply pipeline detection using configuration from comparison system"""
        config = self.main_pipeline_config
        base_method = config['base_method']
        base_params = config.get('base_method_params', {})

        print(f"🎯 Running main pipeline: {base_method} with params {base_params}")

        # Step 1: Run base method detection
        if base_method == 'sequential':
            base_joints = self.run_sequential_detection(stroke, base_params)
        elif base_method == 'angle':
            base_joints = self.run_angle_detection(stroke, base_params)
        elif base_method == 'curvature':
            base_joints = self.run_curvature_detection(stroke, base_params)
        elif base_method == 'velocity':
            base_joints = self.run_velocity_detection(stroke, base_params)
        elif base_method == 'hybrid':
            base_joints = self.run_hybrid_detection(stroke, base_params)
        else:
            print(f"⚠️ Unknown base method {base_method}, falling back to sequential")
            base_joints = self.run_sequential_detection(stroke, {})

        print(f"🔍 Base method {base_method} found {len(base_joints)} joints")

        # Step 2: Apply enabled modifiers
        current_joints = base_joints.copy()
        for modifier_id, enabled in config.get('enabled_modifiers', {}).items():
            if enabled:
                modifier_params = config.get('modifier_params', {}).get(modifier_id, {})
                print(f"🔧 Applying modifier: {modifier_id} with params {modifier_params}")
                current_joints = self.apply_modifier(current_joints, modifier_id)
                print(f"🔧 After {modifier_id}: {len(current_joints)} joints")

        # Step 3: Apply confidence filtering and joint limiting
        confidence_threshold = self.joint_detection_sensitivity
        confidence_filtered_joints = [j for j in current_joints if j.get('confidence', 0.5) >= confidence_threshold]

        if self.limit_joints_enabled and len(confidence_filtered_joints) > self.max_joints_per_stroke:
            confidence_filtered_joints.sort(key=lambda x: x.get('confidence', 0.5), reverse=True)
            final_joints = confidence_filtered_joints[:self.max_joints_per_stroke]
            print(f"⚠️ Joint limiting active: reduced from {len(confidence_filtered_joints)} to {len(final_joints)} joints")
        else:
            final_joints = confidence_filtered_joints

        limiting_status = f" [Limited to {self.max_joints_per_stroke}]" if self.limit_joints_enabled else " [Unlimited]"
        print(f"✅ Pipeline detection found {len(final_joints)} high-confidence joints{limiting_status}")
        for joint in final_joints:
            method = joint.get('detection_method', 'unknown')
            confidence = joint.get('confidence', 0.5)
            print(f"🦴 Joint at {joint['position']} - {method} (confidence: {confidence:.2f})")

        return final_joints

    def legacy_joint_detection(self, stroke, angle_threshold=30):
        """Legacy joint detection system (original sequential + traditional methods)"""
        print(f"🔬 Starting legacy sequential joint detection on stroke with {len(stroke)} points")

        # Step 1: Sequential analysis - process points chronologically
        sequential_joints = self.sequential_joint_analysis(stroke, angle_threshold)
        print(f"⏱️ Sequential analysis found {len(sequential_joints)} temporal joints")

        # Step 2: Traditional multi-method detection for comparison and enhancement
        traditional_joints = self.traditional_multi_method_detection(stroke, angle_threshold)
        print(f"🔍 Traditional methods found {len(traditional_joints)} joints")

        # Step 3: Combine sequential and traditional results
        combined_candidates = []
        combined_candidates.extend([(j, 'sequential', j.get('confidence', 0.9)) for j in sequential_joints])
        combined_candidates.extend([(j, j.get('detection_method', 'traditional'), j.get('confidence', 0.7)) for j in traditional_joints])

        # Step 4: Cluster and validate combined results
        clustered_joints = self.cluster_joint_candidates(combined_candidates)
        print(f"🎯 Clustered {len(combined_candidates)} candidates into {len(clustered_joints)} joints")

        # Step 5: Final validation and scoring
        validated_joints = self.validate_and_score_joints(clustered_joints, stroke)

        # Step 6: Filter by confidence threshold
        confidence_filtered_joints = [j for j in validated_joints if j['confidence'] >= self.joint_detection_sensitivity]

        # Step 7: Apply joint limiting if enabled
        if self.limit_joints_enabled and len(confidence_filtered_joints) > self.max_joints_per_stroke:
            confidence_filtered_joints.sort(key=lambda x: x['confidence'], reverse=True)
            final_joints = confidence_filtered_joints[:self.max_joints_per_stroke]
            print(f"⚠️ Joint limiting active: reduced from {len(confidence_filtered_joints)} to {len(final_joints)} joints")
        else:
            final_joints = confidence_filtered_joints

        limiting_status = f" [Limited to {self.max_joints_per_stroke}]" if self.limit_joints_enabled else " [Unlimited]"
        print(f"✅ Legacy detection found {len(final_joints)} high-confidence joints{limiting_status}")
        for joint in final_joints:
            print(f"🦴 Joint at {joint['position']} - {joint['detection_method']} (confidence: {joint['confidence']:.2f})")

        return final_joints

    def sequential_joint_analysis(self, stroke, angle_threshold=30):
        """Sequential analysis processing stroke points in chronological order"""
        if len(stroke) < 5:
            return []

        print(f"⏱️ Starting sequential analysis from stroke[0] to stroke[{len(stroke)-1}]")

        joints = []

        # Step 1: Analyze start and end points as potential joints
        start_end_joints = self.analyze_stroke_endpoints(stroke)
        joints.extend(start_end_joints)

        # Step 2: Progressive direction change tracking
        direction_joints = self.track_progressive_direction_changes(stroke, angle_threshold)
        joints.extend(direction_joints)

        # Step 3: Temporal pause point detection
        pause_joints = self.detect_temporal_pause_points(stroke)
        joints.extend(pause_joints)

        # Step 4: Cumulative change analysis
        cumulative_joints = self.analyze_cumulative_changes(stroke)
        joints.extend(cumulative_joints)

        print(f"📊 Sequential components: {len(start_end_joints)} endpoints, {len(direction_joints)} direction, {len(pause_joints)} pause, {len(cumulative_joints)} cumulative")

        return joints

    def analyze_stroke_endpoints(self, stroke):
        """Analyze start and end points as potential joint locations"""
        joints = []

        # Start point analysis
        if len(stroke) >= 3:
            start_joint = self.evaluate_endpoint_as_joint(stroke, 0, is_start=True)
            if start_joint:
                joints.append(start_joint)
                print(f"🎯 Start point joint detected at {start_joint['position']}")

        # End point analysis
        if len(stroke) >= 3:
            end_joint = self.evaluate_endpoint_as_joint(stroke, len(stroke)-1, is_start=False)
            if end_joint:
                joints.append(end_joint)
                print(f"🎯 End point joint detected at {end_joint['position']}")

        return joints

    def evaluate_endpoint_as_joint(self, stroke, endpoint_idx, is_start=True):
        """Evaluate if an endpoint should be considered a joint"""
        if is_start and endpoint_idx < 2:
            # For start point, look at initial direction change
            if len(stroke) < 4:
                return None

            # Check if there's significant direction change near start
            p1, p2, p3, p4 = stroke[0], stroke[1], stroke[2], stroke[3]

            # Calculate initial direction
            initial_dir = math.atan2(p2[1] - p1[1], p2[0] - p1[0])
            # Calculate direction after a few points
            later_dir = math.atan2(p4[1] - p3[1], p4[0] - p3[0])

            angle_diff = abs(math.degrees(later_dir - initial_dir))
            if angle_diff > 180:
                angle_diff = 360 - angle_diff

            if angle_diff > 25:  # Significant direction change near start
                # Calculate rotation for this joint
                rotation = self.calculate_joint_rotation(stroke, 0)

                return {
                    'position': p1,
                    'angle': angle_diff,
                    'index': 0,
                    'rotation': rotation,
                    'confidence': 0.7,
                    'detection_method': 'start_endpoint',
                    'temporal_significance': 'drawing_start'
                }

        elif not is_start and endpoint_idx >= len(stroke) - 1:
            # For end point, similar analysis in reverse
            if len(stroke) < 4:
                return None

            # Check direction change near end
            p1, p2, p3, p4 = stroke[-4], stroke[-3], stroke[-2], stroke[-1]

            early_dir = math.atan2(p2[1] - p1[1], p2[0] - p1[0])
            final_dir = math.atan2(p4[1] - p3[1], p4[0] - p3[0])

            angle_diff = abs(math.degrees(final_dir - early_dir))
            if angle_diff > 180:
                angle_diff = 360 - angle_diff

            if angle_diff > 25:
                # Calculate rotation for this joint
                rotation = self.calculate_joint_rotation(stroke, len(stroke) - 1)

                return {
                    'position': stroke[-1],
                    'angle': angle_diff,
                    'index': len(stroke) - 1,
                    'rotation': rotation,
                    'confidence': 0.7,
                    'detection_method': 'end_endpoint',
                    'temporal_significance': 'drawing_end'
                }

        return None

    def track_progressive_direction_changes(self, stroke, angle_threshold=30):
        """Track direction changes progressively as we move through the stroke"""
        if len(stroke) < 5:
            return []

        joints = []
        current_direction = None
        direction_history = []
        cumulative_change = 0
        last_significant_point = 0

        print(f"📈 Tracking progressive direction changes with threshold {angle_threshold}°")

        # Process points sequentially from start to end
        for i in range(2, len(stroke) - 1):
            # Calculate current direction
            p_prev = stroke[i-1]
            p_curr = stroke[i]
            p_next = stroke[i+1]

            # Get vectors
            v1 = (p_curr[0] - p_prev[0], p_curr[1] - p_prev[1])
            v2 = (p_next[0] - p_curr[0], p_next[1] - p_curr[1])

            # Calculate angle change at this point
            angle_change = self.calculate_angle_between_vectors(v1, v2)

            # Track direction history
            direction_history.append({
                'index': i,
                'angle_change': angle_change,
                'position': p_curr
            })

            # Update cumulative change
            cumulative_change += abs(angle_change)

            # Check for significant direction change
            if abs(angle_change) > angle_threshold:
                # This is a significant change - potential joint
                joint_confidence = self.calculate_progressive_confidence(
                    angle_change, cumulative_change, i - last_significant_point, direction_history[-5:]
                )

                if joint_confidence > 0.5:
                    # Calculate rotation for this joint
                    rotation = self.calculate_joint_rotation(stroke, i)

                    joint = {
                        'position': p_curr,
                        'angle': angle_change,
                        'index': i,
                        'rotation': rotation,
                        'confidence': joint_confidence,
                        'detection_method': 'progressive_direction',
                        'cumulative_change': cumulative_change,
                        'sequence_position': i / len(stroke)  # Relative position in drawing sequence
                    }
                    joints.append(joint)
                    print(f"📍 Progressive joint at index {i}: angle={angle_change:.1f}°, confidence={joint_confidence:.2f}")

                    # Reset tracking for next segment
                    cumulative_change = 0
                    last_significant_point = i

        return joints

    def calculate_progressive_confidence(self, angle_change, cumulative_change, points_since_last, recent_history):
        """Calculate confidence for a progressive direction change joint"""
        base_confidence = min(abs(angle_change) / 90.0, 1.0)  # Normalize to 90 degrees

        # Boost confidence if this follows a period of consistent direction
        if points_since_last > 5:
            base_confidence *= 1.2

        # Boost confidence if cumulative change suggests this is a major turning point
        if cumulative_change > 45:
            base_confidence *= 1.1

        # Consider recent history - smoother changes get higher confidence
        if len(recent_history) >= 3:
            recent_angles = [h['angle_change'] for h in recent_history[-3:]]
            if all(abs(a) < 15 for a in recent_angles[:-1]) and abs(recent_angles[-1]) > 30:
                base_confidence *= 1.15  # Sudden change after smooth drawing

        return min(base_confidence, 1.0)

    def detect_temporal_pause_points(self, stroke):
        """Detect pause points based on drawing speed and temporal patterns"""
        if len(stroke) < 6:
            return []

        joints = []

        # Calculate inter-point distances (proxy for drawing speed)
        distances = []
        for i in range(1, len(stroke)):
            dist = math.sqrt((stroke[i][0] - stroke[i-1][0])**2 + (stroke[i][1] - stroke[i-1][1])**2)
            distances.append(dist)

        if not distances:
            return []

        # Find pause points (very small distances indicating slow drawing)
        avg_distance = sum(distances) / len(distances)
        pause_threshold = avg_distance * 0.2  # 20% of average speed

        print(f"⏸️ Detecting pause points (avg_speed={avg_distance:.2f}, threshold={pause_threshold:.2f})")

        # Look for sequences of slow drawing followed by faster drawing
        in_pause = False
        pause_start = 0

        for i in range(len(distances)):
            is_slow = distances[i] < pause_threshold

            if is_slow and not in_pause:
                # Starting a pause
                in_pause = True
                pause_start = i
            elif not is_slow and in_pause:
                # Ending a pause
                in_pause = False
                pause_duration = i - pause_start

                # If pause was significant, mark as potential joint
                if pause_duration >= 2:  # At least 2 points of slow drawing
                    pause_center = pause_start + pause_duration // 2
                    if pause_center < len(stroke):
                        # Check if there's also a direction change at this point
                        direction_change = self.get_direction_change_at_point(stroke, pause_center + 1)

                        confidence = self.calculate_pause_confidence(pause_duration, direction_change, avg_distance, distances[pause_center])

                        if confidence > 0.4:
                            # Calculate rotation for this joint
                            rotation = self.calculate_joint_rotation(stroke, pause_center + 1)

                            joint = {
                                'position': stroke[pause_center + 1],
                                'angle': direction_change,
                                'index': pause_center + 1,
                                'rotation': rotation,
                                'confidence': confidence,
                                'detection_method': 'temporal_pause',
                                'pause_duration': pause_duration,
                                'speed_ratio': distances[pause_center] / avg_distance
                            }
                            joints.append(joint)
                            print(f"⏸️ Pause joint at index {pause_center + 1}: duration={pause_duration}, confidence={confidence:.2f}")

        return joints

    def get_direction_change_at_point(self, stroke, index):
        """Get direction change at a specific point"""
        if index < 1 or index >= len(stroke) - 1:
            return 0

        p1 = stroke[index - 1]
        p2 = stroke[index]
        p3 = stroke[index + 1]

        v1 = (p2[0] - p1[0], p2[1] - p1[1])
        v2 = (p3[0] - p2[0], p3[1] - p2[1])

        return self.calculate_angle_between_vectors(v1, v2)

    def calculate_pause_confidence(self, pause_duration, direction_change, avg_speed, pause_speed):
        """Calculate confidence for a pause-based joint"""
        # Base confidence from pause duration
        duration_confidence = min(pause_duration / 5.0, 1.0)

        # Boost if there's also a direction change
        direction_confidence = min(abs(direction_change) / 45.0, 1.0)

        # Boost if the pause was very slow compared to average
        speed_confidence = min((avg_speed - pause_speed) / avg_speed, 1.0)

        # Combine factors
        total_confidence = (duration_confidence * 0.4 + direction_confidence * 0.4 + speed_confidence * 0.2)

        return min(total_confidence, 1.0)

    def analyze_cumulative_changes(self, stroke):
        """Analyze cumulative direction changes to identify major turning points"""
        if len(stroke) < 8:
            return []

        joints = []

        # Calculate cumulative angle changes along the stroke
        cumulative_angles = [0]  # Start with 0
        total_change = 0

        for i in range(2, len(stroke) - 1):
            p1 = stroke[i-1]
            p2 = stroke[i]
            p3 = stroke[i+1]

            v1 = (p2[0] - p1[0], p2[1] - p1[1])
            v2 = (p3[0] - p2[0], p3[1] - p2[1])

            angle_change = self.calculate_angle_between_vectors(v1, v2)
            total_change += abs(angle_change)
            cumulative_angles.append(total_change)

        if not cumulative_angles or max(cumulative_angles) == 0:
            return []

        print(f"📊 Cumulative analysis: total_change={total_change:.1f}°, max_cumulative={max(cumulative_angles):.1f}°")

        # Find points where cumulative change accelerates significantly
        window_size = min(5, len(cumulative_angles) // 4)

        for i in range(window_size, len(cumulative_angles) - window_size):
            # Calculate rate of change before and after this point
            before_window = cumulative_angles[i-window_size:i]
            after_window = cumulative_angles[i:i+window_size]

            if len(before_window) < 2 or len(after_window) < 2:
                continue

            # Rate of change (slope)
            before_rate = (before_window[-1] - before_window[0]) / len(before_window)
            after_rate = (after_window[-1] - after_window[0]) / len(after_window)

            # Look for significant acceleration in cumulative change
            rate_change = after_rate - before_rate

            if rate_change > 5.0:  # Significant acceleration in direction change
                # This point represents a major turning point
                stroke_index = i + 1  # Adjust for cumulative array offset
                if stroke_index < len(stroke):
                    direction_change = self.get_direction_change_at_point(stroke, stroke_index)

                    confidence = self.calculate_cumulative_confidence(
                        rate_change, total_change, cumulative_angles[i], i / len(cumulative_angles)
                    )

                    if confidence > 0.5:
                        # Calculate rotation for this joint
                        rotation = self.calculate_joint_rotation(stroke, stroke_index)

                        joint = {
                            'position': stroke[stroke_index],
                            'angle': direction_change,
                            'index': stroke_index,
                            'rotation': rotation,
                            'confidence': confidence,
                            'detection_method': 'cumulative_change',
                            'rate_change': rate_change,
                            'cumulative_at_point': cumulative_angles[i],
                            'sequence_position': i / len(cumulative_angles)
                        }
                        joints.append(joint)
                        print(f"📈 Cumulative joint at index {stroke_index}: rate_change={rate_change:.2f}, confidence={confidence:.2f}")

        return joints

    def calculate_cumulative_confidence(self, rate_change, total_change, cumulative_at_point, sequence_position):
        """Calculate confidence for cumulative change joint"""
        # Base confidence from rate of change acceleration
        rate_confidence = min(rate_change / 20.0, 1.0)

        # Boost if this represents a significant portion of total change
        if total_change > 0:
            proportion_confidence = min(cumulative_at_point / total_change, 1.0)
        else:
            proportion_confidence = 0.5

        # Boost if it's not at the very start or end (more likely to be a real joint)
        position_confidence = 1.0 - abs(sequence_position - 0.5) * 0.5

        # Combine factors
        total_confidence = (rate_confidence * 0.5 + proportion_confidence * 0.3 + position_confidence * 0.2)

        return min(total_confidence, 1.0)

    def traditional_multi_method_detection(self, stroke, angle_threshold=30):
        """Traditional multi-method detection for comparison with sequential analysis"""
        if len(stroke) < 5:
            return []

        # Pre-process stroke with smoothing
        smoothed_stroke = self.smooth_stroke(stroke) if len(stroke) > self.smoothing_window_size else stroke

        # Multi-method joint detection
        angle_joints = self.detect_angle_based_joints(smoothed_stroke, angle_threshold)
        curvature_joints = self.detect_curvature_joints(smoothed_stroke)
        velocity_joints = self.detect_velocity_joints(stroke) if self.stroke_velocity_analysis else []
        gradual_joints = self.detect_gradual_angle_changes(smoothed_stroke)

        # Combine all traditional methods
        all_joints = []
        all_joints.extend(angle_joints)
        all_joints.extend(curvature_joints)
        all_joints.extend(velocity_joints)
        all_joints.extend(gradual_joints)

        # Add detection method tags
        for joint in angle_joints:
            joint['detection_method'] = 'angle'
        for joint in curvature_joints:
            joint['detection_method'] = 'curvature'
        for joint in velocity_joints:
            joint['detection_method'] = 'velocity'
        for joint in gradual_joints:
            joint['detection_method'] = 'gradual'

        return all_joints

    def smooth_stroke(self, stroke):
        """Apply smoothing to reduce noise while preserving joint information"""
        if len(stroke) <= self.smoothing_window_size:
            return stroke

        smoothed = []
        half_window = self.smoothing_window_size // 2

        for i in range(len(stroke)):
            # Calculate window bounds
            start_idx = max(0, i - half_window)
            end_idx = min(len(stroke), i + half_window + 1)

            # Average points in window
            window_points = stroke[start_idx:end_idx]
            avg_x = sum(p[0] for p in window_points) / len(window_points)
            avg_y = sum(p[1] for p in window_points) / len(window_points)

            smoothed.append((avg_x, avg_y))

        return smoothed

    def detect_angle_based_joints(self, stroke, angle_threshold):
        """Original angle-based detection with improvements"""
        joints = []

        for i in range(1, len(stroke) - 1):
            p1 = stroke[i-1]
            p2 = stroke[i]
            p3 = stroke[i+1]

            # Calculate vectors
            v1 = (p2[0] - p1[0], p2[1] - p1[1])
            v2 = (p3[0] - p2[0], p3[1] - p2[1])

            # Calculate angle between vectors
            angle = self.calculate_angle_between_vectors(v1, v2)

            # Adaptive threshold based on vector lengths
            min_length = min(self.vector_length(v1), self.vector_length(v2))
            adaptive_threshold = angle_threshold * (1.0 - min(min_length / 50.0, 0.5))

            if abs(angle) > adaptive_threshold:
                # Calculate rotation for this joint
                rotation = self.calculate_joint_rotation(stroke, i)

                joint_info = {
                    'position': p2,
                    'angle': angle,
                    'index': i,
                    'rotation': rotation,
                    'vector_lengths': (self.vector_length(v1), self.vector_length(v2))
                }
                joints.append(joint_info)

        return joints

    def detect_curvature_joints(self, stroke):
        """Detect joints using enhanced curvature analysis"""
        if len(stroke) < 5:
            return []

        joints = []
        curvatures = self.calculate_curvature(stroke)

        if not curvatures:
            return []

        # Calculate adaptive threshold based on stroke characteristics
        max_curvature = max(curvatures) if curvatures else 0
        mean_curvature = sum(curvatures) / len(curvatures) if curvatures else 0
        std_curvature = math.sqrt(sum((c - mean_curvature)**2 for c in curvatures) / len(curvatures)) if curvatures else 0

        # Use adaptive threshold: base threshold + portion of standard deviation
        adaptive_threshold = max(self.curvature_threshold, mean_curvature + 0.5 * std_curvature)

        print(f"🔍 Curvature analysis: max={max_curvature:.4f}, mean={mean_curvature:.4f}, std={std_curvature:.4f}, threshold={adaptive_threshold:.4f}")

        # Find significant curvature peaks (local maxima)
        for i in range(2, len(curvatures) - 2):  # Leave more margin for peak detection
            current_curvature = curvatures[i]

            # Check if this is a local maximum
            is_peak = (current_curvature > curvatures[i-1] and
                      current_curvature > curvatures[i+1] and
                      current_curvature > curvatures[i-2] and
                      current_curvature > curvatures[i+2])

            # Check if curvature is significant
            is_significant = current_curvature > adaptive_threshold

            if is_peak and is_significant:
                # Calculate angle change at this point for additional validation
                angle_change = self.calculate_angle_change_at_point(stroke, i+1)

                # Calculate rotation for this joint
                rotation = self.calculate_joint_rotation(stroke, i+1)

                joint_info = {
                    'position': stroke[i+1],  # Offset for curvature calculation
                    'angle': angle_change,
                    'index': i+1,
                    'rotation': rotation,
                    'curvature': current_curvature,
                    'curvature_ratio': current_curvature / max_curvature if max_curvature > 0 else 0
                }
                joints.append(joint_info)
                print(f"🦴 Curvature joint found at index {i+1}: curvature={current_curvature:.4f}, angle={angle_change:.1f}°")

        return joints

    def calculate_angle_change_at_point(self, stroke, index):
        """Calculate the angle change at a specific point in the stroke"""
        if index < 1 or index >= len(stroke) - 1:
            return 0

        p1 = stroke[index - 1]
        p2 = stroke[index]
        p3 = stroke[index + 1]

        # Calculate vectors
        v1 = (p2[0] - p1[0], p2[1] - p1[1])
        v2 = (p3[0] - p2[0], p3[1] - p2[1])

        # Calculate angle between vectors
        return self.calculate_angle_between_vectors(v1, v2)

    def calculate_curvature(self, stroke):
        """Calculate curvature at each point using proper mathematical curvature formula"""
        if len(stroke) < 3:
            return []

        curvatures = []

        for i in range(1, len(stroke) - 1):
            p1 = stroke[i-1]
            p2 = stroke[i]
            p3 = stroke[i+1]

            # Calculate vectors
            v1 = (p2[0] - p1[0], p2[1] - p1[1])  # Vector from p1 to p2
            v2 = (p3[0] - p2[0], p3[1] - p2[1])  # Vector from p2 to p3

            # Calculate magnitudes
            mag1 = math.sqrt(v1[0]**2 + v1[1]**2)
            mag2 = math.sqrt(v2[0]**2 + v2[1]**2)

            if mag1 == 0 or mag2 == 0:
                curvatures.append(0)
                continue

            # Normalize vectors
            u1 = (v1[0] / mag1, v1[1] / mag1)
            u2 = (v2[0] / mag2, v2[1] / mag2)

            # Calculate cross product magnitude (sine of angle between vectors)
            cross_product = abs(u1[0] * u2[1] - u1[1] * u2[0])

            # Calculate dot product (cosine of angle between vectors)
            dot_product = u1[0] * u2[0] + u1[1] * u2[1]

            # Calculate curvature using the formula: κ = |dT/ds| where T is unit tangent
            # For discrete points, this approximates to the change in direction
            avg_segment_length = (mag1 + mag2) / 2
            if avg_segment_length > 0:
                # Curvature is the rate of change of direction
                curvature = cross_product / avg_segment_length
            else:
                curvature = 0

            curvatures.append(curvature)

        # Apply smoothing to reduce noise
        if len(curvatures) >= self.curvature_smoothing_window:
            smoothed_curvatures = []
            half_window = self.curvature_smoothing_window // 2

            for i in range(len(curvatures)):
                start_idx = max(0, i - half_window)
                end_idx = min(len(curvatures), i + half_window + 1)
                window_values = curvatures[start_idx:end_idx]
                smoothed_value = sum(window_values) / len(window_values)
                smoothed_curvatures.append(smoothed_value)

            return smoothed_curvatures

        return curvatures

    def detect_velocity_joints(self, stroke):
        """Detect joints based on drawing velocity changes (pause points)"""
        if len(stroke) < 4:
            return []

        joints = []
        velocities = self.calculate_stroke_velocities(stroke)

        # Find significant velocity drops (pause points)
        avg_velocity = sum(velocities) / len(velocities)
        velocity_threshold = avg_velocity * 0.3  # 30% of average velocity

        for i in range(1, len(velocities) - 1):
            if (velocities[i] < velocity_threshold and
                velocities[i] < velocities[i-1] and
                velocities[i] < velocities[i+1]):

                # Calculate rotation for this joint
                rotation = self.calculate_joint_rotation(stroke, i+1)

                joint_info = {
                    'position': stroke[i+1],  # Offset for velocity calculation
                    'angle': 0,  # Will be calculated later
                    'index': i+1,
                    'rotation': rotation,
                    'velocity': velocities[i]
                }
                joints.append(joint_info)

        return joints

    def calculate_stroke_velocities(self, stroke):
        """Calculate drawing velocity between consecutive points"""
        if len(stroke) < 2:
            return []

        velocities = []

        for i in range(1, len(stroke)):
            p1 = stroke[i-1]
            p2 = stroke[i]

            # Distance between points (proxy for velocity)
            distance = math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
            velocities.append(distance)

        return velocities

    def detect_gradual_angle_changes(self, stroke):
        """Detect joints with gradual angle changes using sliding window"""
        if len(stroke) < 7:  # Need enough points for sliding window
            return []

        joints = []
        window_size = 5

        for i in range(window_size, len(stroke) - window_size):
            # Calculate angle change over window
            start_idx = i - window_size
            end_idx = i + window_size

            # Get direction vectors for start and end of window
            mid_point = stroke[i]
            start_vector = (mid_point[0] - stroke[start_idx][0],
                          mid_point[1] - stroke[start_idx][1])
            end_vector = (stroke[end_idx][0] - mid_point[0],
                        stroke[end_idx][1] - mid_point[1])

            # Calculate total angle change
            total_angle = self.calculate_angle_between_vectors(start_vector, end_vector)

            # Check for significant gradual change
            if abs(total_angle) > 20:  # Gradual changes are typically smaller
                # Calculate rotation for this joint
                rotation = self.calculate_joint_rotation(stroke, i)

                joint_info = {
                    'position': mid_point,
                    'angle': total_angle,
                    'index': i,
                    'rotation': rotation,
                    'window_size': window_size
                }
                joints.append(joint_info)

        return joints

    def cluster_joint_candidates(self, candidates):
        """Cluster nearby joint candidates that likely represent the same joint"""
        if not candidates:
            return []

        # Sort candidates by position for efficient clustering
        candidates.sort(key=lambda x: (x[0]['position'][0], x[0]['position'][1]))

        clusters = []
        used = set()

        for i, (candidate, method, confidence) in enumerate(candidates):
            if i in used:
                continue

            # Start new cluster
            cluster = [(candidate, method, confidence)]
            used.add(i)

            # Find nearby candidates
            for j, (other_candidate, other_method, other_confidence) in enumerate(candidates[i+1:], i+1):
                if j in used:
                    continue

                distance = self.point_distance(candidate['position'], other_candidate['position'])
                if distance <= self.joint_clustering_distance:
                    cluster.append((other_candidate, other_method, other_confidence))
                    used.add(j)

            clusters.append(cluster)

        # Merge each cluster into a single joint
        merged_joints = []
        for cluster in clusters:
            merged_joint = self.merge_cluster_to_joint(cluster)
            merged_joints.append(merged_joint)

        return merged_joints

    def merge_cluster_to_joint(self, cluster):
        """Merge a cluster of joint candidates into a single joint"""
        if len(cluster) == 1:
            candidate, method, confidence = cluster[0]
            candidate['detection_method'] = method
            candidate['confidence'] = confidence
            return candidate

        # Calculate weighted average position
        total_weight = sum(conf for _, _, conf in cluster)
        avg_x = sum(cand['position'][0] * conf for cand, _, conf in cluster) / total_weight
        avg_y = sum(cand['position'][1] * conf for cand, _, conf in cluster) / total_weight

        # Find best angle and method
        best_candidate, best_method, best_confidence = max(cluster, key=lambda x: x[2])

        # Combine detection methods
        methods = list(set(method for _, method, _ in cluster))
        combined_method = '+'.join(methods)

        # Boost confidence for multiple detection methods
        confidence_boost = min(0.2 * (len(cluster) - 1), 0.3)
        final_confidence = min(1.0, best_confidence + confidence_boost)

        merged_joint = {
            'position': (avg_x, avg_y),
            'angle': best_candidate['angle'],
            'index': best_candidate['index'],
            'detection_method': combined_method,
            'confidence': final_confidence,
            'cluster_size': len(cluster)
        }

        return merged_joint

    def validate_and_score_joints(self, joints, original_stroke):
        """Validate joints and assign final confidence scores"""
        validated_joints = []

        for joint in joints:
            # Base confidence from detection method
            confidence = joint.get('confidence', 0.5)

            # Anatomical validation - check if joint is in reasonable position
            anatomical_score = self.validate_anatomical_position(joint, original_stroke)
            confidence *= anatomical_score

            # Distance validation - joints shouldn't be too close to stroke ends
            distance_score = self.validate_joint_distance_from_ends(joint, original_stroke)
            confidence *= distance_score

            # Angle validation - very small angles are less likely to be joints
            angle_score = self.validate_joint_angle(joint)
            confidence *= angle_score

            joint['confidence'] = confidence
            validated_joints.append(joint)

        return validated_joints

    def validate_anatomical_position(self, joint, stroke):
        """Validate joint position based on anatomical constraints"""
        # For now, simple validation - could be enhanced with ML
        stroke_length = self.calculate_stroke_length(stroke)

        # Joints near the middle of strokes are more likely
        joint_index = joint['index']
        relative_position = joint_index / len(stroke)

        # Prefer joints not too close to ends (0.1 to 0.9 range)
        if 0.1 <= relative_position <= 0.9:
            return 1.0
        elif 0.05 <= relative_position <= 0.95:
            return 0.8
        else:
            return 0.6

    def validate_joint_distance_from_ends(self, joint, stroke):
        """Ensure joints aren't too close to stroke endpoints"""
        joint_index = joint['index']
        stroke_length = len(stroke)

        min_distance_from_end = min(joint_index, stroke_length - joint_index - 1)

        if min_distance_from_end >= 5:
            return 1.0
        elif min_distance_from_end >= 3:
            return 0.8
        else:
            return 0.5

    def validate_joint_angle(self, joint):
        """Validate joint based on angle characteristics - enhanced for curved joints"""
        angle = abs(joint.get('angle', 0))
        curvature = joint.get('curvature', 0)
        detection_method = joint.get('detection_method', '')

        # For curvature-detected joints, be more lenient with angle requirements
        if 'curvature' in detection_method:
            # Curved joints may have smaller angle changes but high curvature
            if curvature > 0:
                curvature_score = min(1.0, curvature * 20)  # Scale curvature to 0-1
                angle_score = max(0.4, angle / 45.0)  # More lenient angle scoring
                combined_score = (curvature_score + angle_score) / 2
                return max(0.3, combined_score)  # Minimum score for curvature joints

        # Standard angle validation for other detection methods
        if angle >= 30:
            return 1.0
        elif angle >= 15:
            return 0.8
        elif angle >= 5:
            return 0.6
        else:
            return 0.3

    def vector_length(self, vector):
        """Calculate the length of a vector"""
        return math.sqrt(vector[0]**2 + vector[1]**2)

    def point_distance(self, p1, p2):
        """Calculate distance between two points"""
        return math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)

    # Enhanced Joint Detection Control Methods
    def toggle_velocity_analysis(self):
        """Toggle stroke velocity analysis on/off"""
        self.stroke_velocity_analysis = not self.stroke_velocity_analysis
        status = "enabled" if self.stroke_velocity_analysis else "disabled"
        print(f"🏃 Velocity analysis {status}")
        self.update_status(f"Velocity analysis {status}")
        messagebox.showinfo("Velocity Analysis", f"Stroke velocity analysis is now {status}")

    def toggle_joint_limiting(self):
        """Toggle joint limiting on/off"""
        self.limit_joints_enabled = not self.limit_joints_enabled
        status = f"enabled (max {self.max_joints_per_stroke} joints per stroke)" if self.limit_joints_enabled else "disabled (unlimited joints)"
        print(f"🔢 Joint limiting {status}")
        self.update_status(f"Joint limiting: {status}")
        messagebox.showinfo("Joint Limiting",
                          f"Joint limiting is now {status}.\n\n"
                          f"{'Joints will be limited to the highest confidence detections.' if self.limit_joints_enabled else 'All detected joints will be used.'}")

    def set_detection_sensitivity(self, sensitivity):
        """Set joint detection sensitivity level"""
        self.joint_detection_sensitivity = sensitivity
        print(f"🎯 Detection sensitivity set to {sensitivity:.1f}")
        self.update_status(f"Detection sensitivity: {sensitivity:.1f}")
        messagebox.showinfo("Sensitivity", f"Joint detection sensitivity set to {sensitivity:.1f}")

    def test_enhanced_detection(self):
        """Test enhanced detection on current strokes"""
        if not self.all_strokes:
            messagebox.showwarning("Warning", "No strokes to test detection on!")
            return

        print("🧪 Testing enhanced joint detection...")

        total_joints = 0
        for i, stroke in enumerate(self.all_strokes):
            joints = self.find_angle_changes(stroke)
            total_joints += len(joints)
            print(f"Stroke {i+1}: {len(joints)} joints detected")

        messagebox.showinfo("Detection Test",
                          f"Enhanced detection found {total_joints} joints across {len(self.all_strokes)} strokes.\n"
                          f"Check console for detailed results.")

    def debug_joint_detection(self):
        """Debug joint detection with detailed analysis"""
        if not self.all_strokes:
            messagebox.showwarning("Warning", "No strokes to debug!")
            return

        print("\n" + "="*60)
        print("🔬 ENHANCED JOINT DETECTION DEBUG")
        print("="*60)
        print(f"Settings:")
        print(f"  Velocity Analysis: {self.stroke_velocity_analysis}")
        print(f"  Detection Sensitivity: {self.joint_detection_sensitivity}")
        print(f"  Joint Limiting: {self.limit_joints_enabled} (max: {self.max_joints_per_stroke})")
        print(f"  Curvature Threshold: {self.curvature_threshold}")
        print(f"  Curvature Smoothing: {self.curvature_smoothing_window}")
        print(f"  Clustering Distance: {self.joint_clustering_distance}px")
        print(f"  Smoothing Window: {self.smoothing_window_size}")
        print()

        for i, stroke in enumerate(self.all_strokes):
            print(f"Stroke {i+1} Analysis:")
            print(f"  Points: {len(stroke)}")
            print(f"  Length: {self.calculate_stroke_length(stroke):.1f}px")

            # Run detection with debug info
            joints = self.find_angle_changes(stroke)

            print(f"  Final Joints: {len(joints)}")
            for j, joint in enumerate(joints):
                print(f"    Joint {j+1}: {joint['position']} - {joint['detection_method']} (conf: {joint['confidence']:.2f})")
            print()

        print("="*60 + "\n")

    def calculate_angle_between_vectors(self, v1, v2):
        """Calculate angle between two vectors in degrees"""
        # Handle zero vectors
        if (v1[0] == 0 and v1[1] == 0) or (v2[0] == 0 and v2[1] == 0):
            return 0

        # Calculate dot product and magnitudes
        dot_product = v1[0] * v2[0] + v1[1] * v2[1]
        mag1 = math.sqrt(v1[0]**2 + v1[1]**2)
        mag2 = math.sqrt(v2[0]**2 + v2[1]**2)

        # Calculate angle
        cos_angle = dot_product / (mag1 * mag2)
        cos_angle = max(-1, min(1, cos_angle))  # Clamp to [-1, 1]

        angle_rad = math.acos(cos_angle)
        angle_deg = math.degrees(angle_rad)

        # Calculate cross product to determine direction
        cross_product = v1[0] * v2[1] - v1[1] * v2[0]
        if cross_product < 0:
            angle_deg = -angle_deg

        return angle_deg

    def calculate_joint_rotation(self, stroke, joint_index):
        """Calculate rotation angle for a joint based on incoming and outgoing stroke directions"""
        if joint_index < 1 or joint_index >= len(stroke) - 1:
            # For endpoint joints, calculate rotation based on available direction
            if joint_index == 0 and len(stroke) > 1:
                # Start point - use direction to next point
                v = (stroke[1][0] - stroke[0][0], stroke[1][1] - stroke[0][1])
                return math.degrees(math.atan2(v[1], v[0]))
            elif joint_index == len(stroke) - 1 and len(stroke) > 1:
                # End point - use direction from previous point
                v = (stroke[-1][0] - stroke[-2][0], stroke[-1][1] - stroke[-2][1])
                return math.degrees(math.atan2(v[1], v[0]))
            else:
                return 0.0

        # For middle joints, calculate average direction
        p_prev = stroke[joint_index - 1]
        p_curr = stroke[joint_index]
        p_next = stroke[joint_index + 1]

        # Calculate incoming and outgoing vectors
        v_in = (p_curr[0] - p_prev[0], p_curr[1] - p_prev[1])
        v_out = (p_next[0] - p_curr[0], p_next[1] - p_curr[1])

        # Calculate angles for both vectors
        angle_in = math.atan2(v_in[1], v_in[0])
        angle_out = math.atan2(v_out[1], v_out[0])

        # Calculate average angle (handling angle wrapping)
        angle_diff = angle_out - angle_in
        if angle_diff > math.pi:
            angle_diff -= 2 * math.pi
        elif angle_diff < -math.pi:
            angle_diff += 2 * math.pi

        avg_angle = angle_in + angle_diff / 2

        return math.degrees(avg_angle)

    def classify_body_part(self, stroke):
        """Enhanced body part classification using figure analysis and relative positioning"""
        if len(stroke) < 2:
            return "unknown"

        # Calculate stroke characteristics
        stroke_info = self.analyze_stroke_characteristics(stroke)

        # Get figure context (bounding box and center)
        figure_context = self.calculate_figure_context()

        # Classify using enhanced logic
        classification = self.classify_with_context(stroke_info, figure_context)

        print(f"📐 Stroke analysis: center=({stroke_info['center_x']:.1f}, {stroke_info['center_y']:.1f}), length={stroke_info['length']:.1f}")

        return classification

    def analyze_stroke_characteristics(self, stroke):
        """Analyze detailed characteristics of a stroke"""
        start_point = stroke[0]
        end_point = stroke[-1]
        center_x = sum(p[0] for p in stroke) / len(stroke)
        center_y = sum(p[1] for p in stroke) / len(stroke)

        # Calculate stroke direction and length
        dx = end_point[0] - start_point[0]
        dy = end_point[1] - start_point[1]
        length = math.sqrt(dx**2 + dy**2)

        # Calculate orientation angle (0° = horizontal right, 90° = vertical down)
        angle = math.degrees(math.atan2(dy, dx))
        if angle < 0:
            angle += 360

        # Determine if stroke is primarily vertical, horizontal, or diagonal
        is_vertical = abs(angle - 90) < 30 or abs(angle - 270) < 30
        is_horizontal = abs(angle) < 30 or abs(angle - 180) < 30
        is_diagonal = not (is_vertical or is_horizontal)

        # Calculate aspect ratio and compactness
        min_x = min(p[0] for p in stroke)
        max_x = max(p[0] for p in stroke)
        min_y = min(p[1] for p in stroke)
        max_y = max(p[1] for p in stroke)

        width = max_x - min_x
        height = max_y - min_y
        aspect_ratio = height / width if width > 0 else float('inf')

        # Check if stroke might be circular/curved (head candidate)
        is_compact = width > 0 and height > 0 and abs(aspect_ratio - 1.0) < 0.5

        return {
            'start_point': start_point,
            'end_point': end_point,
            'center_x': center_x,
            'center_y': center_y,
            'length': length,
            'angle': angle,
            'is_vertical': is_vertical,
            'is_horizontal': is_horizontal,
            'is_diagonal': is_diagonal,
            'width': width,
            'height': height,
            'aspect_ratio': aspect_ratio,
            'is_compact': is_compact,
            'bounding_box': (min_x, min_y, max_x, max_y)
        }

    def calculate_figure_context(self):
        """Calculate overall figure context including bounding box and center"""
        if not self.all_strokes:
            return {
                'center_x': 400, 'center_y': 300,
                'min_x': 0, 'max_x': 800, 'min_y': 0, 'max_y': 600,
                'width': 800, 'height': 600
            }

        # Calculate overall bounding box of all strokes
        all_points = []
        for stroke in self.all_strokes:
            all_points.extend(stroke)

        if not all_points:
            return {
                'center_x': 400, 'center_y': 300,
                'min_x': 0, 'max_x': 800, 'min_y': 0, 'max_y': 600,
                'width': 800, 'height': 600
            }

        min_x = min(p[0] for p in all_points)
        max_x = max(p[0] for p in all_points)
        min_y = min(p[1] for p in all_points)
        max_y = max(p[1] for p in all_points)

        center_x = (min_x + max_x) / 2
        center_y = (min_y + max_y) / 2
        width = max_x - min_x
        height = max_y - min_y

        return {
            'center_x': center_x,
            'center_y': center_y,
            'min_x': min_x,
            'max_x': max_x,
            'min_y': min_y,
            'max_y': max_y,
            'width': width,
            'height': height
        }

    def classify_with_context(self, stroke_info, figure_context):
        """Classify body part using stroke characteristics and figure context"""
        center_x = stroke_info['center_x']
        center_y = stroke_info['center_y']
        length = stroke_info['length']

        # Calculate relative position within figure
        fig_center_x = figure_context['center_x']
        fig_center_y = figure_context['center_y']
        fig_height = figure_context['height']
        fig_width = figure_context['width']

        # Avoid division by zero
        if fig_height == 0:
            fig_height = 1
        if fig_width == 0:
            fig_width = 1

        # Relative position (0.0 = top/left, 1.0 = bottom/right)
        rel_y = (center_y - figure_context['min_y']) / fig_height
        rel_x = (center_x - figure_context['min_x']) / fig_width

        # Distance from figure center
        dist_from_center_x = abs(center_x - fig_center_x)
        dist_from_center_y = abs(center_y - fig_center_y)

        print(f"🎯 Classification context: rel_pos=({rel_x:.2f}, {rel_y:.2f}), fig_center=({fig_center_x:.1f}, {fig_center_y:.1f})")

        # Priority 1: Head detection (top area, compact or circular)
        if rel_y < 0.25:  # Top 25% of figure
            if (stroke_info['is_compact'] and length < fig_height * 0.3) or \
               (length < fig_height * 0.2 and stroke_info['width'] > 0 and stroke_info['height'] > 0):
                return "head"

        # Priority 2: Torso detection (vertical stroke near center)
        if stroke_info['is_vertical'] and dist_from_center_x < fig_width * 0.2:
            if 0.2 < rel_y < 0.7 and length > fig_height * 0.2:
                return "torso"
            elif rel_y < 0.3 and length < fig_height * 0.15:
                return "neck"

        # Priority 3: Arms detection (upper area, horizontal/diagonal)
        if rel_y < 0.6:  # Upper 60% of figure
            if stroke_info['is_horizontal'] or stroke_info['is_diagonal']:
                # Determine left vs right based on position relative to figure center
                if center_x < fig_center_x:
                    return "left_arm"
                else:
                    return "right_arm"

        # Priority 4: Legs detection (lower area, typically vertical or diagonal)
        if rel_y > 0.4:  # Lower 60% of figure (overlap with arms for flexibility)
            if stroke_info['is_vertical'] or stroke_info['is_diagonal']:
                # Determine left vs right based on position relative to figure center
                if center_x < fig_center_x:
                    return "left_leg"
                else:
                    return "right_leg"

        # Fallback classification based on position only
        return self.fallback_classification(stroke_info, figure_context)

    def fallback_classification(self, stroke_info, figure_context):
        """Fallback classification when primary methods don't match"""
        center_x = stroke_info['center_x']
        center_y = stroke_info['center_y']
        fig_center_x = figure_context['center_x']
        fig_center_y = figure_context['center_y']
        fig_height = figure_context['height']

        # Avoid division by zero
        if fig_height == 0:
            fig_height = 1

        # Relative vertical position
        rel_y = (center_y - figure_context['min_y']) / fig_height

        # Simple position-based classification
        if rel_y < 0.3:
            # Upper area - could be head, neck, or upper torso
            if stroke_info['length'] < fig_height * 0.2:
                return "head"
            else:
                return "torso"
        elif rel_y < 0.7:
            # Middle area - likely arms or torso
            if center_x < fig_center_x:
                return "left_arm"
            else:
                return "right_arm"
        else:
            # Lower area - likely legs
            if center_x < fig_center_x:
                return "left_leg"
            else:
                return "right_leg"

    def validate_and_refine_classifications(self):
        """Post-process classifications to fix common errors and improve accuracy"""
        if not self.body_parts:
            return

        print("🔍 Validating and refining body part classifications...")

        # Collect all classifications
        classifications = {}
        stroke_infos = {}

        for stroke_id, part_data in self.body_parts.items():
            stroke = part_data['stroke']
            body_part = part_data['body_part']
            classifications[stroke_id] = body_part
            stroke_infos[stroke_id] = self.analyze_stroke_characteristics(stroke)

        # Get figure context
        figure_context = self.calculate_figure_context()

        # Apply refinement rules
        refined_classifications = self.apply_refinement_rules(classifications, stroke_infos, figure_context)

        # Update classifications
        changes_made = 0
        for stroke_id, new_classification in refined_classifications.items():
            if stroke_id in self.body_parts and self.body_parts[stroke_id]['body_part'] != new_classification:
                old_classification = self.body_parts[stroke_id]['body_part']
                self.body_parts[stroke_id]['body_part'] = new_classification
                print(f"🔄 Refined {stroke_id}: {old_classification} → {new_classification}")
                changes_made += 1

        if changes_made > 0:
            print(f"✅ Refined {changes_made} classifications")
        else:
            print("✅ No refinements needed")

    def apply_refinement_rules(self, classifications, stroke_infos, figure_context):
        """Apply specific rules to refine classifications"""
        refined = classifications.copy()

        # Rule 1: Ensure we have at most one head
        head_candidates = [sid for sid, cls in classifications.items() if cls == "head"]
        if len(head_candidates) > 1:
            # Keep the topmost and most compact one as head
            best_head = min(head_candidates,
                          key=lambda sid: (stroke_infos[sid]['center_y'],
                                         -stroke_infos[sid]['is_compact']))
            for sid in head_candidates:
                if sid != best_head:
                    # Reclassify other heads based on position
                    if stroke_infos[sid]['center_y'] < figure_context['center_y']:
                        refined[sid] = "torso"
                    else:
                        refined[sid] = "unknown"

        # Rule 2: Ensure torso is reasonably positioned
        torso_candidates = [sid for sid, cls in classifications.items() if cls == "torso"]
        for sid in torso_candidates:
            stroke_info = stroke_infos[sid]
            rel_y = (stroke_info['center_y'] - figure_context['min_y']) / max(figure_context['height'], 1)

            # Torso should be in middle area and vertical
            if rel_y > 0.8 or not stroke_info['is_vertical']:
                # Reclassify based on position
                if stroke_info['center_x'] < figure_context['center_x']:
                    refined[sid] = "left_arm" if rel_y < 0.6 else "left_leg"
                else:
                    refined[sid] = "right_arm" if rel_y < 0.6 else "right_leg"

        # Rule 3: Balance left/right classifications
        refined = self.balance_left_right_classifications(refined, stroke_infos, figure_context)

        return refined

    def balance_left_right_classifications(self, classifications, stroke_infos, figure_context):
        """Ensure left/right classifications are properly balanced and positioned"""
        refined = classifications.copy()
        fig_center_x = figure_context['center_x']

        # Check arms
        left_arms = [sid for sid, cls in classifications.items() if cls == "left_arm"]
        right_arms = [sid for sid, cls in classifications.items() if cls == "right_arm"]

        # Fix arms that are on the wrong side
        for sid in left_arms:
            if stroke_infos[sid]['center_x'] > fig_center_x:
                refined[sid] = "right_arm"
                print(f"🔄 Fixed arm side: {sid} moved to right_arm")

        for sid in right_arms:
            if stroke_infos[sid]['center_x'] < fig_center_x:
                refined[sid] = "left_arm"
                print(f"🔄 Fixed arm side: {sid} moved to left_arm")

        # Check legs
        left_legs = [sid for sid, cls in classifications.items() if cls == "left_leg"]
        right_legs = [sid for sid, cls in classifications.items() if cls == "right_leg"]

        # Fix legs that are on the wrong side
        for sid in left_legs:
            if stroke_infos[sid]['center_x'] > fig_center_x:
                refined[sid] = "right_leg"
                print(f"🔄 Fixed leg side: {sid} moved to right_leg")

        for sid in right_legs:
            if stroke_infos[sid]['center_x'] < fig_center_x:
                refined[sid] = "left_leg"
                print(f"🔄 Fixed leg side: {sid} moved to left_leg")

        return refined

    # Skeleton Generation Methods
    def generate_skeleton(self):
        """Generate skeleton from analyzed strokes"""
        print("🦴 Generating skeleton...")

        if not self.body_parts:
            print("❌ No analyzed strokes found")
            messagebox.showwarning("Warning", "Analyze strokes first!")
            return

        # Clear previous skeleton
        self.bones = []
        self.joints = []

        # Generate bones and joints for each body part
        for stroke_id, part_data in self.body_parts.items():
            stroke = part_data['stroke']
            body_part = part_data['body_part']
            joints_in_stroke = part_data['joints']

            print(f"🦴 Processing {body_part} with {len(joints_in_stroke)} joints")

            # Create bones from stroke segments
            bones_in_part = self.create_bones_from_stroke(stroke, joints_in_stroke, body_part)
            self.bones.extend(bones_in_part)

            print(f"✅ Created {len(bones_in_part)} bones for {body_part}")

        # Draw the skeleton
        self.draw_skeleton()

        print(f"✅ Skeleton generated with {len(self.bones)} bones and {len(self.joints)} joints")
        self.update_status(f"Generated skeleton: {len(self.bones)} bones, {len(self.joints)} joints")

    def create_bones_from_stroke(self, stroke, joints_in_stroke, body_part):
        """Create bone objects from a stroke and its joints"""
        bones = []

        if len(stroke) < 2:
            return bones

        # Sort joints by their index in the stroke (with defensive check)
        joints_in_stroke.sort(key=lambda j: j.get('index', 0))

        # Create segments between joints (and from start/end to joints)
        segment_points = [0]  # Start with stroke beginning

        for joint in joints_in_stroke:
            segment_points.append(joint['index'])

        segment_points.append(len(stroke) - 1)  # End with stroke end

        # Create bones for each segment
        for i in range(len(segment_points) - 1):
            start_idx = segment_points[i]
            end_idx = segment_points[i + 1]

            start_point = stroke[start_idx]
            end_point = stroke[end_idx]

            # Calculate bone properties
            bone_length = math.sqrt((end_point[0] - start_point[0])**2 +
                                  (end_point[1] - start_point[1])**2)

            # Calculate rotation angle
            dx = end_point[0] - start_point[0]
            dy = end_point[1] - start_point[1]
            rotation = math.degrees(math.atan2(dy, dx))

            # Determine bone type based on body part and position
            bone_type = self.determine_bone_type(body_part, i, len(segment_points) - 1)

            bone = {
                'id': f"bone_{len(self.bones) + len(bones)}",
                'start': start_point,
                'end': end_point,
                'length': bone_length,
                'rotation': rotation,
                'body_part': body_part,
                'bone_type': bone_type,
                'stroke_segment': stroke[start_idx:end_idx+1]
            }

            bones.append(bone)

            # Create joints at bone ends
            if i == 0:  # First bone - create joint at start
                joint = self.create_joint(start_point, f"{body_part}_start", rotation)
                self.joints.append(joint)

            # Always create joint at end
            joint_name = f"{body_part}_{bone_type}_end"
            if i == len(segment_points) - 2:  # Last bone
                joint_name = f"{body_part}_end"

            joint = self.create_joint(end_point, joint_name, rotation)
            self.joints.append(joint)

            print(f"🦴 Created {bone_type} bone: {bone_length:.1f}px, {rotation:.1f}°")

        return bones

    def determine_bone_type(self, body_part, segment_index, total_segments):
        """Determine the specific bone type based on body part and position"""
        bone_type_map = {
            'left_arm': ['upper_arm', 'lower_arm', 'hand'],
            'right_arm': ['upper_arm', 'lower_arm', 'hand'],
            'left_leg': ['upper_leg', 'lower_leg', 'foot'],
            'right_leg': ['upper_leg', 'lower_leg', 'foot'],
            'torso': ['spine'],
            'neck': ['neck'],
            'head': ['skull']
        }

        if body_part not in bone_type_map:
            return 'unknown'

        bone_types = bone_type_map[body_part]

        # Map segment index to bone type
        if total_segments == 1:
            return bone_types[0] if bone_types else 'unknown'

        # Distribute segments across available bone types
        type_index = min(segment_index, len(bone_types) - 1)
        return bone_types[type_index]

    def create_joint(self, position, name, rotation):
        """Create a joint object"""
        joint = {
            'id': f"joint_{len(self.joints)}",
            'position': position,
            'name': name,
            'rotation': rotation
        }
        return joint

    def draw_skeleton(self):
        """Draw the generated skeleton on canvas"""
        print("🎨 Drawing skeleton...")

        # Clear previous skeleton drawings
        self.canvas.delete("skeleton")

        # Draw bones
        for bone in self.bones:
            self.draw_bone(bone)

        # Draw joints
        for joint in self.joints:
            self.draw_joint(joint)

        print(f"✅ Drew {len(self.bones)} bones and {len(self.joints)} joints")

    def draw_bone(self, bone):
        """Draw a single bone on the canvas"""
        start_x, start_y = bone['start']
        end_x, end_y = bone['end']

        # Draw bone line
        line_id = self.canvas.create_line(
            start_x, start_y, end_x, end_y,
            fill=self.bone_color,
            width=3,
            tags=("skeleton", "bone", bone['id'])
        )

        # Add bone label
        mid_x = (start_x + end_x) / 2
        mid_y = (start_y + end_y) / 2

        label_text = f"{bone['bone_type']}\n{bone['rotation']:.1f}°"

        self.canvas.create_text(
            mid_x, mid_y - 15,
            text=label_text,
            fill=self.bone_color,
            font=("Arial", 8),
            tags=("skeleton", "bone_label", bone['id'])
        )

    def draw_joint(self, joint):
        """Draw a single joint on the canvas"""
        x, y = joint['position']
        radius = 5

        # Draw joint circle
        self.canvas.create_oval(
            x - radius, y - radius,
            x + radius, y + radius,
            fill=self.joint_color,
            outline=self.joint_color,
            width=2,
            tags=("skeleton", "joint", joint['id'])
        )

        # Add joint label
        label_text = f"{joint['name']}\n{joint['rotation']:.1f}°"

        self.canvas.create_text(
            x, y + 15,
            text=label_text,
            fill=self.joint_color,
            font=("Arial", 7),
            tags=("skeleton", "joint_label", joint['id'])
        )

    # Bone Selection Methods
    def select_bone(self, event):
        """Select a bone using Shift+Click"""
        print(f"🎯 Attempting to select bone at ({event.x}, {event.y})")

        # Find the closest bone to the click point
        closest_bone = None
        min_distance = float('inf')

        for bone in self.bones:
            distance = self.point_to_line_distance(
                (event.x, event.y),
                bone['start'],
                bone['end']
            )

            if distance < min_distance and distance < 20:  # 20px tolerance
                min_distance = distance
                closest_bone = bone

        if closest_bone:
            self.selected_bone = closest_bone
            self.highlight_selected_bone()
            print(f"✅ Selected bone: {closest_bone['id']} ({closest_bone['bone_type']})")
            self.update_status(f"Selected: {closest_bone['bone_type']} bone")
        else:
            self.selected_bone = None
            self.clear_bone_highlights()
            print("❌ No bone found near click point")
            self.update_status("No bone selected")

    def point_to_line_distance(self, point, line_start, line_end):
        """Calculate distance from a point to a line segment"""
        px, py = point
        x1, y1 = line_start
        x2, y2 = line_end

        # Calculate line length squared
        line_length_sq = (x2 - x1)**2 + (y2 - y1)**2

        if line_length_sq == 0:
            # Line is actually a point
            return math.sqrt((px - x1)**2 + (py - y1)**2)

        # Calculate projection of point onto line
        t = max(0, min(1, ((px - x1) * (x2 - x1) + (py - y1) * (y2 - y1)) / line_length_sq))

        # Find closest point on line segment
        closest_x = x1 + t * (x2 - x1)
        closest_y = y1 + t * (y2 - y1)

        # Return distance to closest point
        return math.sqrt((px - closest_x)**2 + (py - closest_y)**2)

    def highlight_selected_bone(self):
        """Highlight the currently selected bone"""
        if not self.selected_bone:
            return

        # Clear previous highlights
        self.clear_bone_highlights()

        # Highlight selected bone
        start_x, start_y = self.selected_bone['start']
        end_x, end_y = self.selected_bone['end']

        self.canvas.create_line(
            start_x, start_y, end_x, end_y,
            fill="#FFFF00",  # Yellow highlight
            width=5,
            tags=("highlight",)
        )

        print(f"🔆 Highlighted bone: {self.selected_bone['id']}")

    def clear_bone_highlights(self):
        """Clear all bone highlights"""
        self.canvas.delete("highlight")

    # Bone Manipulation Methods
    def split_selected_bone(self):
        """Split the selected bone in half"""
        if not self.selected_bone:
            print("❌ No bone selected for splitting")
            messagebox.showwarning("Warning", "Select a bone first using Shift+Click")
            return

        print(f"✂️ Splitting bone: {self.selected_bone['id']}")

        # Calculate midpoint
        start_x, start_y = self.selected_bone['start']
        end_x, end_y = self.selected_bone['end']
        mid_x = (start_x + end_x) / 2
        mid_y = (start_y + end_y) / 2

        # Find the best split point by analyzing the original stroke
        stroke_segment = self.selected_bone['stroke_segment']
        split_point = self.find_best_split_point(stroke_segment, (mid_x, mid_y))

        print(f"🎯 Split point: {split_point}")

        # Create two new bones
        bone1 = {
            'id': f"bone_{len(self.bones)}",
            'start': self.selected_bone['start'],
            'end': split_point,
            'length': math.sqrt((split_point[0] - start_x)**2 + (split_point[1] - start_y)**2),
            'rotation': math.degrees(math.atan2(split_point[1] - start_y, split_point[0] - start_x)),
            'body_part': self.selected_bone['body_part'],
            'bone_type': f"{self.selected_bone['bone_type']}_1",
            'stroke_segment': []
        }

        bone2 = {
            'id': f"bone_{len(self.bones) + 1}",
            'start': split_point,
            'end': self.selected_bone['end'],
            'length': math.sqrt((end_x - split_point[0])**2 + (end_y - split_point[1])**2),
            'rotation': math.degrees(math.atan2(end_y - split_point[1], end_x - split_point[0])),
            'body_part': self.selected_bone['body_part'],
            'bone_type': f"{self.selected_bone['bone_type']}_2",
            'stroke_segment': []
        }

        # Create joint at split point
        split_joint = self.create_joint(split_point, f"{self.selected_bone['bone_type']}_split", bone1['rotation'])

        # Remove original bone and add new ones
        self.bones.remove(self.selected_bone)
        self.bones.extend([bone1, bone2])
        self.joints.append(split_joint)

        # Clear selection and redraw
        self.selected_bone = None
        self.clear_bone_highlights()
        self.draw_skeleton()

        print(f"✅ Bone split into 2 parts with new joint at {split_point}")
        self.update_status(f"Split bone into 2 parts")

    def find_best_split_point(self, stroke_segment, default_midpoint):
        """Find the best point to split a bone based on the original stroke"""
        if not stroke_segment or len(stroke_segment) < 3:
            return default_midpoint

        # Find the point in the stroke closest to the midpoint
        min_distance = float('inf')
        best_point = default_midpoint

        for point in stroke_segment:
            distance = math.sqrt((point[0] - default_midpoint[0])**2 +
                               (point[1] - default_midpoint[1])**2)
            if distance < min_distance:
                min_distance = distance
                best_point = point

        return best_point

    # Mirroring Methods
    def set_mirror_origin(self):
        """Set the X origin for limb mirroring"""
        print("🪞 Click to set mirror origin...")
        self.update_status("Click to set mirror origin X coordinate")

        # Bind temporary click handler
        self.canvas.bind("<Button-1>", self.on_mirror_origin_click)

    def on_mirror_origin_click(self, event):
        """Handle click to set mirror origin"""
        self.mirror_origin_x = event.x
        print(f"🪞 Mirror origin set to X = {self.mirror_origin_x}")

        # Draw vertical line to show mirror axis
        self.canvas.delete("mirror_axis")
        self.canvas.create_line(
            self.mirror_origin_x, 0,
            self.mirror_origin_x, 1200,
            fill="#FF00FF",  # Magenta
            width=2,
            dash=(5, 5),
            tags=("mirror_axis",)
        )

        # Restore normal click binding
        self.canvas.bind("<Button-1>", self.start_drawing)

        self.update_status(f"Mirror origin set at X = {self.mirror_origin_x}")

    def mirror_limbs(self):
        """Mirror limbs across the set origin"""
        if self.mirror_origin_x is None:
            print("❌ No mirror origin set")
            messagebox.showwarning("Warning", "Set mirror origin first!")
            return

        print(f"🪞 Mirroring limbs across X = {self.mirror_origin_x}")

        # Find limbs to mirror (arms and legs)
        limbs_to_mirror = []
        for bone in self.bones:
            if bone['body_part'] in ['left_arm', 'right_arm', 'left_leg', 'right_leg']:
                limbs_to_mirror.append(bone)

        print(f"🦴 Found {len(limbs_to_mirror)} limb bones to mirror")

        # Create mirrored bones
        mirrored_bones = []
        mirrored_joints = []

        for bone in limbs_to_mirror:
            # Mirror bone positions
            start_x, start_y = bone['start']
            end_x, end_y = bone['end']

            mirrored_start_x = 2 * self.mirror_origin_x - start_x
            mirrored_end_x = 2 * self.mirror_origin_x - end_x

            # Determine mirrored body part
            mirrored_body_part = self.get_mirrored_body_part(bone['body_part'])

            mirrored_bone = {
                'id': f"mirrored_bone_{len(self.bones) + len(mirrored_bones)}",
                'start': (mirrored_start_x, start_y),
                'end': (mirrored_end_x, end_y),
                'length': bone['length'],
                'rotation': 180 - bone['rotation'],  # Mirror rotation
                'body_part': mirrored_body_part,
                'bone_type': bone['bone_type'],
                'stroke_segment': []
            }

            mirrored_bones.append(mirrored_bone)

            # Create mirrored joints
            start_joint = self.create_joint(
                (mirrored_start_x, start_y),
                f"{mirrored_body_part}_start",
                mirrored_bone['rotation']
            )
            end_joint = self.create_joint(
                (mirrored_end_x, end_y),
                f"{mirrored_body_part}_end",
                mirrored_bone['rotation']
            )

            mirrored_joints.extend([start_joint, end_joint])

        # Add mirrored elements
        self.bones.extend(mirrored_bones)
        self.joints.extend(mirrored_joints)

        # Redraw skeleton
        self.draw_skeleton()

        print(f"✅ Mirrored {len(mirrored_bones)} bones and {len(mirrored_joints)} joints")
        self.update_status(f"Mirrored {len(mirrored_bones)} limb bones")

    def get_mirrored_body_part(self, body_part):
        """Get the mirrored version of a body part"""
        mirror_map = {
            'left_arm': 'right_arm',
            'right_arm': 'left_arm',
            'left_leg': 'right_leg',
            'right_leg': 'left_leg'
        }
        return mirror_map.get(body_part, body_part)

    # File Operations
    def new_drawing(self):
        """Start a new drawing"""
        print("🆕 Starting new drawing...")

        # Clear all data
        self.all_strokes = []
        self.bones = []
        self.joints = []
        self.body_parts = {}
        self.selected_bone = None
        self.mirror_origin_x = None

        # Clear canvas
        self.canvas.delete("all")

        # Reset zoom
        self.zoom_factor = 1.0

        print("✅ New drawing started")
        self.update_status("New drawing started")

    def save_drawing(self):
        """Save current drawing to file"""
        print("💾 Saving drawing...")
        # Implementation would save to JSON file
        messagebox.showinfo("Info", "Save functionality not implemented yet")

    def load_drawing(self):
        """Load drawing from file"""
        print("📂 Loading drawing...")
        # Implementation would load from JSON file
        messagebox.showinfo("Info", "Load functionality not implemented yet")

    # Body Part Highlighting
    def highlight_body_part(self, body_part):
        """Highlight a specific body part"""
        print(f"🔆 Highlighting body part: {body_part}")

        # Clear previous highlights
        self.canvas.delete("body_part_highlight")

        # Find and highlight bones of this body part
        highlighted_count = 0
        for bone in self.bones:
            if bone['body_part'] == body_part:
                start_x, start_y = bone['start']
                end_x, end_y = bone['end']

                self.canvas.create_line(
                    start_x, start_y, end_x, end_y,
                    fill="#00FF00",  # Green highlight
                    width=6,
                    tags=("body_part_highlight",)
                )
                highlighted_count += 1

        print(f"✅ Highlighted {highlighted_count} bones for {body_part}")
        self.update_status(f"Highlighted {body_part}: {highlighted_count} bones")

    def detect_body_parts(self):
        """Detect and classify body parts from current strokes"""
        print("🔍 Detecting body parts...")

        if not self.all_strokes:
            messagebox.showwarning("Warning", "No strokes to analyze!")
            return

        # This would be enhanced with more sophisticated detection
        self.analyze_strokes()

        # Show detection results
        body_part_counts = {}
        for part_data in self.body_parts.values():
            body_part = part_data['body_part']
            body_part_counts[body_part] = body_part_counts.get(body_part, 0) + 1

        result_text = "Detected body parts:\n"
        for part, count in body_part_counts.items():
            result_text += f"- {part}: {count} stroke(s)\n"

        messagebox.showinfo("Body Part Detection", result_text)
        print("✅ Body part detection complete")

    def clear_analysis(self):
        """Clear all analysis results"""
        print("🧹 Clearing analysis...")

        self.body_parts = {}
        self.bones = []
        self.joints = []
        self.selected_bone = None

        # Clear skeleton drawings
        self.canvas.delete("skeleton")
        self.canvas.delete("highlight")
        self.canvas.delete("body_part_highlight")

        print("✅ Analysis cleared")
        self.update_status("Analysis cleared")

    # Debug Methods
    def debug_print_strokes(self):
        """Print stroke information to console"""
        print("\n" + "="*50)
        print("🔍 STROKE DEBUG INFO")
        print("="*50)

        for i, stroke in enumerate(self.all_strokes):
            length = self.calculate_stroke_length(stroke)
            print(f"Stroke {i+1}: {len(stroke)} points, {length:.2f}px length")
            print(f"  Start: {stroke[0]}, End: {stroke[-1]}")

        print(f"\nTotal strokes: {len(self.all_strokes)}")
        print("="*50 + "\n")

    def debug_print_bones(self):
        """Print bone information to console"""
        print("\n" + "="*50)
        print("🦴 BONE DEBUG INFO")
        print("="*50)

        for i, bone in enumerate(self.bones):
            print(f"Bone {i+1} ({bone['id']}):")
            print(f"  Type: {bone['bone_type']}")
            print(f"  Body Part: {bone['body_part']}")
            print(f"  Start: {bone['start']}")
            print(f"  End: {bone['end']}")
            print(f"  Length: {bone['length']:.2f}px")
            print(f"  Rotation: {bone['rotation']:.2f}°")
            print()

        print(f"Total bones: {len(self.bones)}")
        print("="*50 + "\n")

    def debug_print_joints(self):
        """Print joint information to console"""
        print("\n" + "="*50)
        print("🦴 JOINT DEBUG INFO")
        print("="*50)

        for i, joint in enumerate(self.joints):
            print(f"Joint {i+1} ({joint['id']}):")
            print(f"  Name: {joint['name']}")
            print(f"  Position: {joint['position']}")
            print(f"  Rotation: {joint['rotation']:.2f}°")
            print()

        print(f"Total joints: {len(self.joints)}")
        print("="*50 + "\n")

    def debug_print_body_parts(self):
        """Print body part information to console"""
        print("\n" + "="*50)
        print("🏷️ BODY PART DEBUG INFO")
        print("="*50)

        for stroke_id, part_data in self.body_parts.items():
            print(f"{stroke_id}:")
            print(f"  Body Part: {part_data['body_part']}")
            print(f"  Joints: {len(part_data['joints'])}")
            for j, joint in enumerate(part_data['joints']):
                print(f"    Joint {j+1}: {joint['position']}, {joint['angle']:.1f}°")
            print()

        print(f"Total analyzed strokes: {len(self.body_parts)}")
        print("="*50 + "\n")

    def debug_canvas_info(self):
        """Show canvas information"""
        bbox = self.canvas.bbox("all")
        scroll_region = self.canvas.cget("scrollregion")

        info_text = f"""Canvas Information:
Zoom Factor: {self.zoom_factor:.2f}
Bounding Box: {bbox}
Scroll Region: {scroll_region}
Canvas Size: {self.canvas.winfo_width()}x{self.canvas.winfo_height()}
Total Items: {len(self.canvas.find_all())}
Mirror Origin X: {self.mirror_origin_x}
Selected Bone: {self.selected_bone['id'] if self.selected_bone else 'None'}
"""

        messagebox.showinfo("Canvas Debug Info", info_text)
        print("🖼️ Canvas debug info displayed")

    # Utility Methods
    def update_status(self, message):
        """Update status bar message"""
        self.status_bar.config(text=message)
        print(f"📊 Status: {message}")

    def run(self):
        """Start the application"""
        print("🚀 Starting Stick Figure Analyzer...")
        self.root.mainloop()

    # Joint Detection Comparison System
    def open_joint_comparison(self):
        """Open the joint detection method comparison window"""
        if self.comparison_window and self.comparison_window.winfo_exists():
            self.comparison_window.lift()
            return

        if not self.all_strokes:
            tk.messagebox.showwarning("No Strokes", "Please draw some strokes first before comparing joint detection methods.")
            return

        print("🔬 Opening joint detection comparison window...")
        self.create_comparison_window()

    def create_comparison_window(self):
        """Create the joint detection method comparison window"""
        self.comparison_window = tk.Toplevel(self.root)
        self.comparison_window.title("Joint Detection Method Comparison")
        self.comparison_window.geometry("1400x900")
        self.comparison_window.resizable(True, True)

        # Create main frame with paned window for resizable sections
        main_paned = ttk.PanedWindow(self.comparison_window, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel for controls
        control_frame = ttk.Frame(main_paned)
        main_paned.add(control_frame, weight=1)

        # Right panel for visualization
        viz_frame = ttk.Frame(main_paned)
        main_paned.add(viz_frame, weight=3)

        # Setup control panel
        self.setup_comparison_controls(control_frame)

        # Setup visualization panel
        self.setup_comparison_visualization(viz_frame)

        # Initialize detection methods
        self.initialize_detection_methods()

        # Run initial comparison
        self.run_method_comparison()

        print("✅ Joint detection comparison window created")

    def setup_comparison_controls(self, parent):
        """Setup the control panel for method comparison - refactored architecture"""
        # Title
        title_label = ttk.Label(parent, text="Joint Detection Pipeline", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 10))

        # Create scrollable frame for controls
        canvas = tk.Canvas(parent, highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Setup base method selection
        self.setup_base_method_controls(scrollable_frame)

        # Add separator
        separator1 = ttk.Separator(scrollable_frame, orient='horizontal')
        separator1.pack(fill='x', pady=10)

        # Setup modifier controls
        self.setup_modifier_controls(scrollable_frame)

        # Add separator
        separator2 = ttk.Separator(scrollable_frame, orient='horizontal')
        separator2.pack(fill='x', pady=10)

        # Global controls
        self.setup_global_controls(scrollable_frame)

    def setup_base_method_controls(self, parent):
        """Setup base detection method selection"""
        # Section title
        base_title = ttk.Label(parent, text="Base Detection Method", font=("Arial", 12, "bold"))
        base_title.pack(anchor="w", pady=(0, 5))

        # Description
        desc_label = ttk.Label(parent, text="Choose one base detection algorithm:", font=("Arial", 9))
        desc_label.pack(anchor="w", pady=(0, 10))

        # Base method selection frame
        base_frame = ttk.Frame(parent)
        base_frame.pack(fill="x", pady=(0, 10))

        # Base method configurations
        base_methods_config = {
            'sequential': {
                'name': 'Sequential Analysis',
                'description': 'Processes stroke points in chronological order',
                'color': self.base_method_colors['sequential'],
                'params': {
                    'angle_threshold': {'min': 10, 'max': 60, 'default': 30, 'label': 'Angle Threshold'},
                    'temporal_sensitivity': {'min': 0.1, 'max': 2.0, 'default': 1.0, 'label': 'Temporal Sensitivity'},
                    'confidence_threshold': {'min': 0.1, 'max': 1.0, 'default': 0.5, 'label': 'Confidence Threshold'}
                }
            },
            'angle': {
                'name': 'Traditional Angle Detection',
                'description': 'Detects joints based on angle changes between line segments',
                'color': self.base_method_colors['angle'],
                'params': {
                    'angle_threshold': {'min': 10, 'max': 90, 'default': 30, 'label': 'Angle Threshold'},
                    'adaptive_threshold': {'min': 0.0, 'max': 1.0, 'default': 0.5, 'label': 'Adaptive Factor'},
                    'confidence_threshold': {'min': 0.1, 'max': 1.0, 'default': 0.4, 'label': 'Confidence Threshold'}
                }
            },
            'curvature': {
                'name': 'Curvature-based Detection',
                'description': 'Uses mathematical curvature analysis to find joints',
                'color': self.base_method_colors['curvature'],
                'params': {
                    'curvature_sensitivity': {'min': 0.001, 'max': 0.1, 'default': 0.01, 'label': 'Curvature Sensitivity'},
                    'peak_threshold': {'min': 0.1, 'max': 2.0, 'default': 0.5, 'label': 'Peak Threshold'},
                    'confidence_threshold': {'min': 0.1, 'max': 1.0, 'default': 0.3, 'label': 'Confidence Threshold'}
                }
            },
            'velocity': {
                'name': 'Velocity Analysis',
                'description': 'Detects joints based on drawing speed variations',
                'color': self.base_method_colors['velocity'],
                'params': {
                    'velocity_threshold': {'min': 0.1, 'max': 2.0, 'default': 0.6, 'label': 'Velocity Threshold'},
                    'smoothing_window': {'min': 3, 'max': 15, 'default': 5, 'label': 'Smoothing Window'},
                    'confidence_threshold': {'min': 0.1, 'max': 1.0, 'default': 0.4, 'label': 'Confidence Threshold'}
                }
            },
            'hybrid': {
                'name': 'Hybrid Method',
                'description': 'Combines multiple detection approaches with weighted scoring',
                'color': self.base_method_colors['hybrid'],
                'params': {
                    'weight_angle': {'min': 0.0, 'max': 1.0, 'default': 0.4, 'label': 'Angle Weight'},
                    'weight_curvature': {'min': 0.0, 'max': 1.0, 'default': 0.3, 'label': 'Curvature Weight'},
                    'weight_velocity': {'min': 0.0, 'max': 1.0, 'default': 0.3, 'label': 'Velocity Weight'},
                    'confidence_threshold': {'min': 0.1, 'max': 1.0, 'default': 0.6, 'label': 'Confidence Threshold'}
                }
            }
        }

        # Create radio buttons and parameter controls for each base method
        for method_id, config in base_methods_config.items():
            method_frame = ttk.LabelFrame(base_frame, text=config['name'], padding=10)
            method_frame.pack(fill="x", pady=5)

            # Radio button and description
            radio_frame = ttk.Frame(method_frame)
            radio_frame.pack(fill="x", pady=(0, 5))

            radio_btn = ttk.Radiobutton(
                radio_frame,
                text=config['description'],
                variable=self.selected_base_method,
                value=method_id,
                command=self.on_base_method_change
            )
            radio_btn.pack(side="left")

            # Color indicator
            color_canvas = tk.Canvas(radio_frame, width=20, height=15, highlightthickness=0)
            color_canvas.pack(side="right", padx=(5, 0))
            color_canvas.create_rectangle(2, 2, 18, 13, fill=config['color'], outline=config['color'])

            # Parameter controls
            params_frame = ttk.Frame(method_frame)
            params_frame.pack(fill="x", pady=(5, 0))

            # Initialize controls dictionary
            if method_id not in self.base_method_controls:
                self.base_method_controls[method_id] = {'params': {}}

            # Create parameter sliders
            for param_id, param_config in config['params'].items():
                param_frame = ttk.Frame(params_frame)
                param_frame.pack(fill="x", pady=2)

                # Parameter label
                param_label = ttk.Label(param_frame, text=param_config['label'], width=20)
                param_label.pack(side="left")

                # Value display
                value_var = tk.DoubleVar(value=param_config['default'])
                self.base_method_controls[method_id]['params'][param_id] = value_var

                value_label = ttk.Label(param_frame, text=f"{param_config['default']:.3f}", width=8)
                value_label.pack(side="right", padx=(5, 0))

                # Parameter slider
                param_scale = ttk.Scale(
                    param_frame,
                    from_=param_config['min'],
                    to=param_config['max'],
                    variable=value_var,
                    orient=tk.HORIZONTAL,
                    command=lambda v, mid=method_id, pid=param_id, lbl=value_label: self.on_base_parameter_change(mid, pid, lbl, v)
                )
                param_scale.pack(side="right", fill="x", expand=True, padx=(5, 5))

    def setup_modifier_controls(self, parent):
        """Setup post-processing modifier controls"""
        # Section title
        modifier_title = ttk.Label(parent, text="Post-Processing Modifiers", font=("Arial", 12, "bold"))
        modifier_title.pack(anchor="w", pady=(0, 5))

        # Description
        desc_label = ttk.Label(parent, text="Apply modifiers to enhance base method results:", font=("Arial", 9))
        desc_label.pack(anchor="w", pady=(0, 10))

        # Modifier frame
        modifier_frame = ttk.Frame(parent)
        modifier_frame.pack(fill="x", pady=(0, 10))

        # Clustering modifier
        self.setup_clustering_modifier(modifier_frame)

    def setup_clustering_modifier(self, parent):
        """Setup distance-based clustering modifier"""
        cluster_frame = ttk.LabelFrame(parent, text="Distance-based Clustering", padding=10)
        cluster_frame.pack(fill="x", pady=5)

        # Enable checkbox and description
        checkbox_frame = ttk.Frame(cluster_frame)
        checkbox_frame.pack(fill="x", pady=(0, 5))

        cluster_enabled = tk.BooleanVar(value=False)
        self.enabled_modifiers['clustering'] = cluster_enabled

        cluster_checkbox = ttk.Checkbutton(
            checkbox_frame,
            text="Group nearby joint candidates detected by base method",
            variable=cluster_enabled,
            command=self.on_modifier_change
        )
        cluster_checkbox.pack(side="left")

        # Color indicator
        color_canvas = tk.Canvas(checkbox_frame, width=20, height=15, highlightthickness=0)
        color_canvas.pack(side="right", padx=(5, 0))
        color_canvas.create_rectangle(2, 2, 18, 13, fill=self.modifier_colors['clustering'], outline=self.modifier_colors['clustering'])

        # Parameter controls
        params_frame = ttk.Frame(cluster_frame)
        params_frame.pack(fill="x", pady=(5, 0))

        # Initialize controls
        if 'clustering' not in self.modifier_controls:
            self.modifier_controls['clustering'] = {'enabled': cluster_enabled, 'params': {}}

        # Cluster radius parameter
        radius_frame = ttk.Frame(params_frame)
        radius_frame.pack(fill="x", pady=2)

        radius_label = ttk.Label(radius_frame, text="Cluster Radius (px)", width=20)
        radius_label.pack(side="left")

        radius_var = tk.DoubleVar(value=20.0)
        self.modifier_controls['clustering']['params']['cluster_radius'] = radius_var

        radius_value_label = ttk.Label(radius_frame, text="20.0", width=8)
        radius_value_label.pack(side="right", padx=(5, 0))

        radius_scale = ttk.Scale(
            radius_frame,
            from_=5.0,
            to=50.0,
            variable=radius_var,
            orient=tk.HORIZONTAL,
            command=lambda v, lbl=radius_value_label: self.on_modifier_parameter_change('clustering', 'cluster_radius', lbl, v)
        )
        radius_scale.pack(side="right", fill="x", expand=True, padx=(5, 5))

        # Minimum cluster size parameter
        size_frame = ttk.Frame(params_frame)
        size_frame.pack(fill="x", pady=2)

        size_label = ttk.Label(size_frame, text="Min Cluster Size", width=20)
        size_label.pack(side="left")

        size_var = tk.IntVar(value=2)
        self.modifier_controls['clustering']['params']['min_cluster_size'] = size_var

        size_value_label = ttk.Label(size_frame, text="2", width=8)
        size_value_label.pack(side="right", padx=(5, 0))

        size_scale = ttk.Scale(
            size_frame,
            from_=1,
            to=5,
            variable=size_var,
            orient=tk.HORIZONTAL,
            command=lambda v, lbl=size_value_label: self.on_modifier_parameter_change('clustering', 'min_cluster_size', lbl, v)
        )
        size_scale.pack(side="right", fill="x", expand=True, padx=(5, 5))

        # Consolidation method parameter
        method_frame = ttk.Frame(params_frame)
        method_frame.pack(fill="x", pady=2)

        method_label = ttk.Label(method_frame, text="Consolidation Method", width=20)
        method_label.pack(side="left")

        consolidation_var = tk.StringVar(value="weighted_average")
        self.modifier_controls['clustering']['params']['consolidation_method'] = consolidation_var

        method_combo = ttk.Combobox(
            method_frame,
            textvariable=consolidation_var,
            values=["weighted_average", "highest_confidence", "geometric_center"],
            state="readonly",
            width=15
        )
        method_combo.pack(side="right", padx=(5, 0))
        method_combo.bind("<<ComboboxSelected>>", lambda e: self.on_modifier_change())





    def setup_method_controls(self, parent):
        """Setup individual method controls"""
        methods_config = {
            'sequential': {
                'name': 'Sequential Analysis',
                'color': self.method_colors['sequential'],
                'params': {
                    'angle_threshold': {'min': 10, 'max': 60, 'default': 30, 'label': 'Angle Threshold'},
                    'temporal_sensitivity': {'min': 0.1, 'max': 2.0, 'default': 1.0, 'label': 'Temporal Sensitivity'},
                    'confidence_threshold': {'min': 0.1, 'max': 1.0, 'default': 0.5, 'label': 'Confidence Threshold'}
                }
            },
            'angle': {
                'name': 'Traditional Angle Detection',
                'color': self.method_colors['angle'],
                'params': {
                    'angle_threshold': {'min': 10, 'max': 90, 'default': 30, 'label': 'Angle Threshold'},
                    'adaptive_threshold': {'min': 0.0, 'max': 1.0, 'default': 0.5, 'label': 'Adaptive Factor'},
                    'confidence_threshold': {'min': 0.1, 'max': 1.0, 'default': 0.4, 'label': 'Confidence Threshold'}
                }
            },
            'curvature': {
                'name': 'Curvature-based Detection',
                'color': self.method_colors['curvature'],
                'params': {
                    'curvature_sensitivity': {'min': 0.001, 'max': 0.1, 'default': 0.01, 'label': 'Curvature Sensitivity'},
                    'peak_threshold': {'min': 0.1, 'max': 2.0, 'default': 0.5, 'label': 'Peak Threshold'},
                    'confidence_threshold': {'min': 0.1, 'max': 1.0, 'default': 0.3, 'label': 'Confidence Threshold'}
                }
            },
            'velocity': {
                'name': 'Velocity Analysis',
                'color': self.method_colors['velocity'],
                'params': {
                    'velocity_threshold': {'min': 0.1, 'max': 1.0, 'default': 0.3, 'label': 'Velocity Threshold'},
                    'smoothing_window': {'min': 2, 'max': 10, 'default': 3, 'label': 'Smoothing Window'},
                    'confidence_threshold': {'min': 0.1, 'max': 1.0, 'default': 0.4, 'label': 'Confidence Threshold'}
                }
            },
            'clustering': {
                'name': 'Distance-based Clustering',
                'color': self.method_colors['clustering'],
                'params': {
                    'cluster_radius': {'min': 5, 'max': 50, 'default': 20, 'label': 'Cluster Radius'},
                    'min_cluster_size': {'min': 1, 'max': 5, 'default': 2, 'label': 'Min Cluster Size'},
                    'confidence_threshold': {'min': 0.1, 'max': 1.0, 'default': 0.5, 'label': 'Confidence Threshold'}
                }
            },
            'hybrid': {
                'name': 'Hybrid Method',
                'color': self.method_colors['hybrid'],
                'params': {
                    'weight_angle': {'min': 0.0, 'max': 1.0, 'default': 0.4, 'label': 'Angle Weight'},
                    'weight_curvature': {'min': 0.0, 'max': 1.0, 'default': 0.3, 'label': 'Curvature Weight'},
                    'weight_velocity': {'min': 0.0, 'max': 1.0, 'default': 0.3, 'label': 'Velocity Weight'},
                    'confidence_threshold': {'min': 0.1, 'max': 1.0, 'default': 0.6, 'label': 'Confidence Threshold'}
                }
            }
        }

        for method_id, config in methods_config.items():
            self.create_method_control_group(parent, method_id, config)

    def create_method_control_group(self, parent, method_id, config):
        """Create control group for a specific detection method"""
        # Method frame
        method_frame = ttk.LabelFrame(parent, text=config['name'], padding=10)
        method_frame.pack(fill=tk.X, pady=5)

        # Initialize method controls dictionary
        if method_id not in self.method_controls:
            self.method_controls[method_id] = {}

        # Enable/disable checkbox
        enabled_var = tk.BooleanVar(value=True)
        self.method_controls[method_id]['enabled'] = enabled_var

        enable_frame = ttk.Frame(method_frame)
        enable_frame.pack(fill=tk.X, pady=(0, 5))

        enable_check = ttk.Checkbutton(
            enable_frame,
            text="Enable",
            variable=enabled_var,
            command=lambda: self.on_method_parameter_change(method_id)
        )
        enable_check.pack(side=tk.LEFT)

        # Color indicator
        color_label = tk.Label(
            enable_frame,
            text="●",
            fg=config['color'],
            font=("Arial", 16)
        )
        color_label.pack(side=tk.RIGHT)

        # Opacity slider
        opacity_frame = ttk.Frame(method_frame)
        opacity_frame.pack(fill=tk.X, pady=2)

        ttk.Label(opacity_frame, text="Opacity:").pack(side=tk.LEFT)
        opacity_var = tk.DoubleVar(value=1.0)
        self.method_controls[method_id]['opacity'] = opacity_var

        opacity_scale = ttk.Scale(
            opacity_frame,
            from_=0.1,
            to=1.0,
            variable=opacity_var,
            orient=tk.HORIZONTAL,
            command=lambda v: self.on_method_parameter_change(method_id)
        )
        opacity_scale.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(5, 0))

        # Parameter sliders
        for param_id, param_config in config['params'].items():
            self.create_parameter_slider(method_frame, method_id, param_id, param_config)

    def create_parameter_slider(self, parent, method_id, param_id, param_config, is_base_method=True):
        """Create a parameter slider for a detection method"""
        param_frame = ttk.Frame(parent)
        param_frame.pack(fill=tk.X, pady=2)

        # Parameter label
        label_text = f"{param_config['label']}:"
        ttk.Label(param_frame, text=label_text).pack(side=tk.LEFT)

        # Value display
        param_var = tk.DoubleVar(value=param_config['default'])

        # Store parameter variable in appropriate structure
        if is_base_method:
            if 'params' not in self.base_method_controls[method_id]:
                self.base_method_controls[method_id]['params'] = {}
            self.base_method_controls[method_id]['params'][param_id] = param_var
        else:
            if 'params' not in self.modifier_controls[method_id]:
                self.modifier_controls[method_id]['params'] = {}
            self.modifier_controls[method_id]['params'][param_id] = param_var

        value_label = ttk.Label(param_frame, text=f"{param_config['default']:.3f}")
        value_label.pack(side=tk.RIGHT)

        # Parameter slider
        param_scale = ttk.Scale(
            param_frame,
            from_=param_config['min'],
            to=param_config['max'],
            variable=param_var,
            orient=tk.HORIZONTAL,
            command=lambda v: self.on_parameter_slider_change(method_id, param_id, value_label, v, is_base_method)
        )
        param_scale.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(5, 5))

    def on_base_method_change(self):
        """Handle base method selection change"""
        print(f"🔄 Base method changed to: {self.selected_base_method.get()}")
        self.run_pipeline_comparison()

    def on_base_parameter_change(self, method_id, param_id, value_label, value):
        """Handle base method parameter changes"""
        # Update value display
        try:
            float_value = float(value)
            value_label.config(text=f"{float_value:.3f}")
        except:
            pass

        # Run comparison if this is the selected base method
        if method_id == self.selected_base_method.get():
            print(f"🔧 Base method parameter changed: {method_id}.{param_id} = {value}")
            self.run_pipeline_comparison()

    def on_modifier_change(self):
        """Handle modifier enable/disable changes"""
        enabled_mods = [mod for mod, var in self.enabled_modifiers.items() if var.get()]
        print(f"🔄 Enabled modifiers: {enabled_mods}")
        self.run_pipeline_comparison()

    def on_modifier_parameter_change(self, modifier_id, param_id, value_label, value):
        """Handle modifier parameter changes"""
        # Update value display
        try:
            if param_id == 'min_cluster_size':
                int_value = int(float(value))
                value_label.config(text=str(int_value))
            else:
                float_value = float(value)
                value_label.config(text=f"{float_value:.1f}")
        except:
            pass

        # Run comparison if this modifier is enabled
        if modifier_id in self.enabled_modifiers and self.enabled_modifiers[modifier_id].get():
            print(f"🔧 Modifier parameter changed: {modifier_id}.{param_id} = {value}")
            self.run_pipeline_comparison()

    def on_parameter_slider_change(self, method_id, param_id, value_label, value, is_base_method):
        """Handle parameter slider changes for pipeline architecture"""
        # Update value display
        try:
            val = float(value)
            value_label.config(text=f"{val:.3f}")

            # Trigger appropriate update based on method type
            if is_base_method:
                self.on_base_parameter_change(method_id, param_id, val)
            else:
                self.on_modifier_parameter_change(method_id, param_id, val)
        except ValueError:
            pass

    def on_method_parameter_change(self, method_id):
        """Handle method parameter changes"""
        if hasattr(self, 'comparison_window') and self.comparison_window:
            # Re-run detection for this method and update visualization
            self.run_single_method_detection(method_id)
            self.update_comparison_visualization()

    def setup_global_controls(self, parent):
        """Setup global comparison controls"""
        # Global controls frame
        global_frame = ttk.LabelFrame(parent, text="Global Controls", padding=10)
        global_frame.pack(fill=tk.X, pady=10)

        # Comparison mode
        mode_frame = ttk.Frame(global_frame)
        mode_frame.pack(fill=tk.X, pady=5)

        ttk.Label(mode_frame, text="Display Mode:").pack(side=tk.LEFT)

        self.comparison_mode = tk.StringVar(value="overlay")
        mode_combo = ttk.Combobox(
            mode_frame,
            textvariable=self.comparison_mode,
            values=["overlay", "side_by_side", "individual"],
            state="readonly"
        )
        mode_combo.pack(side=tk.RIGHT)
        mode_combo.bind("<<ComboboxSelected>>", lambda e: self.update_comparison_visualization())

        # Performance metrics toggle
        metrics_var = tk.BooleanVar(value=True)
        self.show_metrics = metrics_var

        metrics_check = ttk.Checkbutton(
            global_frame,
            text="Show Performance Metrics",
            variable=metrics_var,
            command=self.update_comparison_visualization
        )
        metrics_check.pack(pady=5)

        # Export/Import/Apply buttons
        button_frame = ttk.Frame(global_frame)
        button_frame.pack(fill=tk.X, pady=5)

        ttk.Button(button_frame, text="Export Settings", command=self.export_comparison_settings).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Import Settings", command=self.import_comparison_settings).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Apply to Main Window", command=self.apply_pipeline_to_main).pack(side=tk.LEFT)

        # Run comparison button
        ttk.Button(global_frame, text="Run Full Comparison", command=self.run_method_comparison).pack(pady=10)

    def setup_comparison_visualization(self, parent):
        """Setup the visualization panel for method comparison"""
        # Title
        title_label = ttk.Label(parent, text="Joint Detection Results", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 10))

        # Canvas for visualization
        viz_frame = ttk.Frame(parent)
        viz_frame.pack(fill=tk.BOTH, expand=True)

        # Create canvas with scrollbars
        self.comparison_canvas = tk.Canvas(
            viz_frame,
            bg="white",
            width=800,
            height=600
        )

        # Scrollbars for comparison canvas
        h_scroll_comp = ttk.Scrollbar(viz_frame, orient=tk.HORIZONTAL, command=self.comparison_canvas.xview)
        v_scroll_comp = ttk.Scrollbar(viz_frame, orient=tk.VERTICAL, command=self.comparison_canvas.yview)
        self.comparison_canvas.configure(xscrollcommand=h_scroll_comp.set, yscrollcommand=v_scroll_comp.set)

        # Pack scrollbars and canvas
        h_scroll_comp.pack(side=tk.BOTTOM, fill=tk.X)
        v_scroll_comp.pack(side=tk.RIGHT, fill=tk.Y)
        self.comparison_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Metrics display frame
        self.metrics_frame = ttk.Frame(parent)
        self.metrics_frame.pack(fill=tk.X, pady=(10, 0))

        # Bind canvas events
        self.comparison_canvas.bind("<Button-1>", self.on_comparison_canvas_click)
        self.comparison_canvas.bind("<Motion>", self.on_comparison_canvas_motion)

    def initialize_detection_methods(self):
        """Initialize all detection methods with default parameters"""
        print("🔧 Initializing detection methods...")

        # Initialize method results storage
        self.method_results = {}

        # Initialize detection methods (will be populated when methods are run)
        for method_id in self.method_colors.keys():
            if method_id != 'ml_pattern':  # Skip ML for now
                self.method_results[method_id] = {
                    'joints': [],
                    'processing_time': 0,
                    'confidence_scores': [],
                    'enabled': True
                }

        print("✅ Detection methods initialized")

    def run_method_comparison(self):
        """Legacy method - redirect to new pipeline"""
        self.run_pipeline_comparison()

    def run_pipeline_comparison(self):
        """Run the detection pipeline: base method + modifiers"""
        if not self.all_strokes:
            return

        print("🔬 Running detection pipeline...")

        # Clear previous results
        self.base_method_results = {}
        self.modifier_results = {}

        # Step 1: Run selected base method
        base_method = self.selected_base_method.get()
        print(f"🎯 Running base method: {base_method}")

        import time
        start_time = time.time()

        # Get base method results
        base_joints = []
        for stroke_idx, stroke in enumerate(self.all_strokes):
            stroke_joints = self.run_base_method_detection(stroke, base_method)
            for joint in stroke_joints:
                joint['stroke_index'] = stroke_idx
                base_joints.append(joint)

        base_processing_time = time.time() - start_time

        # Store base method results
        self.base_method_results[base_method] = {
            'joints': base_joints,
            'processing_time': base_processing_time,
            'confidence_scores': [j.get('confidence', 0.5) for j in base_joints],
            'enabled': True
        }

        print(f"✅ Base method detected {len(base_joints)} joints in {base_processing_time:.3f}s")

        # Step 2: Apply enabled modifiers sequentially
        current_joints = base_joints.copy()

        for modifier_id, enabled_var in self.enabled_modifiers.items():
            if enabled_var.get():
                print(f"🔧 Applying modifier: {modifier_id}")

                start_time = time.time()
                modified_joints = self.apply_modifier(current_joints, modifier_id)
                processing_time = time.time() - start_time

                # Store modifier results
                self.modifier_results[modifier_id] = {
                    'input_joints': len(current_joints),
                    'output_joints': len(modified_joints),
                    'joints': modified_joints,
                    'processing_time': processing_time,
                    'enabled': True
                }

                current_joints = modified_joints
                print(f"✅ Modifier {modifier_id}: {len(current_joints)} joints in {processing_time:.3f}s")

        # Step 3: Apply same filtering as main analyzer for accurate preview
        # This ensures the comparison window shows exactly what "Apply to Main" will produce
        raw_joints_count = len(current_joints)

        # Apply confidence filtering (same as main analyzer)
        confidence_threshold = self.joint_detection_sensitivity
        confidence_filtered_joints = [j for j in current_joints if j.get('confidence', 0.5) >= confidence_threshold]

        # Apply joint limiting (same as main analyzer)
        if self.limit_joints_enabled and len(confidence_filtered_joints) > self.max_joints_per_stroke:
            confidence_filtered_joints.sort(key=lambda x: x.get('confidence', 0.5), reverse=True)
            final_joints = confidence_filtered_joints[:self.max_joints_per_stroke]
            print(f"⚠️ Preview filtering: Joint limiting reduced from {len(confidence_filtered_joints)} to {len(final_joints)} joints")
        else:
            final_joints = confidence_filtered_joints

        # Update current_joints to filtered results for accurate preview
        current_joints = final_joints

        if raw_joints_count != len(current_joints):
            limiting_status = f" [Limited to {self.max_joints_per_stroke}]" if self.limit_joints_enabled else ""
            print(f"🔍 Preview filtering: {raw_joints_count} → {len(current_joints)} joints (confidence ≥ {confidence_threshold:.1f}){limiting_status}")

        # Store final filtered results for visualization
        self.final_pipeline_results = {
            'base_method': base_method,
            'joints': current_joints,
            'raw_joint_count': raw_joints_count,
            'confidence_threshold': confidence_threshold,
            'joint_limiting_enabled': self.limit_joints_enabled,
            'max_joints_per_stroke': self.max_joints_per_stroke
        }

        # Update legacy results for compatibility
        self.method_results = {
            base_method: self.base_method_results[base_method],
            **self.modifier_results
        }

        # Update visualization
        self.update_comparison_visualization()

        total_time = sum(r['processing_time'] for r in [self.base_method_results[base_method]] + list(self.modifier_results.values()))
        limiting_status = f" [Limited to {self.max_joints_per_stroke}]" if self.limit_joints_enabled else " [Unlimited]"
        confidence_info = f" (confidence ≥ {confidence_threshold:.1f})"
        print(f"✅ Pipeline completed: {len(current_joints)} final joints{confidence_info}{limiting_status} in {total_time:.3f}s total")

    def run_base_method_detection(self, stroke, method_id):
        """Run detection for a specific base method"""
        if len(stroke) < 5:
            return []

        # Get method parameters
        params = {}
        if method_id in self.base_method_controls and 'params' in self.base_method_controls[method_id]:
            for param_id, param_var in self.base_method_controls[method_id]['params'].items():
                params[param_id] = param_var.get()

        # Run the appropriate detection method
        if method_id == 'sequential':
            return self.run_sequential_detection(stroke, params)
        elif method_id == 'angle':
            return self.run_angle_detection(stroke, params)
        elif method_id == 'curvature':
            return self.run_curvature_detection(stroke, params)
        elif method_id == 'velocity':
            return self.run_velocity_detection(stroke, params)
        elif method_id == 'hybrid':
            return self.run_hybrid_detection(stroke, params)
        else:
            print(f"⚠️ Unknown base method: {method_id}")
            return []

    def apply_modifier(self, joints, modifier_id):
        """Apply a post-processing modifier to joint results"""
        if modifier_id == 'clustering':
            return self.apply_clustering_modifier(joints)
        else:
            print(f"⚠️ Unknown modifier: {modifier_id}")
            return joints

    def apply_clustering_modifier(self, joints):
        """Apply distance-based clustering to group nearby joints"""
        if not joints:
            return joints

        # Get clustering parameters
        params = {}
        if 'clustering' in self.modifier_controls and 'params' in self.modifier_controls['clustering']:
            for param_id, param_var in self.modifier_controls['clustering']['params'].items():
                params[param_id] = param_var.get()

        cluster_radius = params.get('cluster_radius', 20.0)
        min_cluster_size = int(params.get('min_cluster_size', 2))
        consolidation_method = params.get('consolidation_method', 'weighted_average')

        print(f"🔧 Clustering with radius={cluster_radius}, min_size={min_cluster_size}, method={consolidation_method}")

        # Group joints by stroke to maintain stroke-level clustering
        stroke_groups = {}
        for joint in joints:
            stroke_idx = joint.get('stroke_index', 0)
            if stroke_idx not in stroke_groups:
                stroke_groups[stroke_idx] = []
            stroke_groups[stroke_idx].append(joint)

        clustered_joints = []

        # Apply clustering within each stroke
        for stroke_idx, stroke_joints in stroke_groups.items():
            if len(stroke_joints) < min_cluster_size:
                clustered_joints.extend(stroke_joints)
                continue

            # Distance-based clustering
            clusters = []
            used_joints = set()

            for i, joint in enumerate(stroke_joints):
                if i in used_joints:
                    continue

                cluster = [joint]
                used_joints.add(i)

                # Find nearby joints
                for j, other_joint in enumerate(stroke_joints):
                    if j in used_joints or j == i:
                        continue

                    distance = math.sqrt(
                        (joint['position'][0] - other_joint['position'][0])**2 +
                        (joint['position'][1] - other_joint['position'][1])**2
                    )

                    if distance <= cluster_radius:
                        cluster.append(other_joint)
                        used_joints.add(j)

                if len(cluster) >= min_cluster_size:
                    clusters.append(cluster)
                else:
                    # Keep individual joints that don't form clusters
                    clustered_joints.extend(cluster)

            # Consolidate each cluster into a single joint
            for cluster in clusters:
                consolidated_joint = self.consolidate_cluster(cluster, consolidation_method)
                consolidated_joint['stroke_index'] = stroke_idx
                consolidated_joint['cluster_size'] = len(cluster)
                consolidated_joint['detection_method'] = 'clustering_modifier'
                clustered_joints.append(consolidated_joint)

        return clustered_joints

    def consolidate_cluster(self, cluster, method):
        """Consolidate a cluster of joints into a single joint"""
        if len(cluster) == 1:
            return cluster[0].copy()

        if method == 'weighted_average':
            # Weighted average by confidence
            total_weight = sum(j.get('confidence', 0.5) for j in cluster)
            if total_weight == 0:
                total_weight = len(cluster)

            avg_x = sum(j['position'][0] * j.get('confidence', 0.5) for j in cluster) / total_weight
            avg_y = sum(j['position'][1] * j.get('confidence', 0.5) for j in cluster) / total_weight
            avg_confidence = total_weight / len(cluster)

        elif method == 'highest_confidence':
            # Use the joint with highest confidence
            best_joint = max(cluster, key=lambda j: j.get('confidence', 0.5))
            return best_joint.copy()

        elif method == 'geometric_center':
            # Simple geometric center
            avg_x = sum(j['position'][0] for j in cluster) / len(cluster)
            avg_y = sum(j['position'][1] for j in cluster) / len(cluster)
            avg_confidence = sum(j.get('confidence', 0.5) for j in cluster) / len(cluster)

        else:
            # Default to weighted average
            total_weight = sum(j.get('confidence', 0.5) for j in cluster)
            if total_weight == 0:
                total_weight = len(cluster)

            avg_x = sum(j['position'][0] * j.get('confidence', 0.5) for j in cluster) / total_weight
            avg_y = sum(j['position'][1] * j.get('confidence', 0.5) for j in cluster) / total_weight
            avg_confidence = total_weight / len(cluster)

        # Create consolidated joint
        # Calculate average index for the cluster
        avg_index = sum(j.get('index', 0) for j in cluster) / len(cluster)

        # Calculate average rotation for the cluster
        rotations = [j.get('rotation', 0) for j in cluster if 'rotation' in j]
        if rotations:
            # Handle angle wrapping for rotation averaging
            sin_sum = sum(math.sin(math.radians(r)) for r in rotations)
            cos_sum = sum(math.cos(math.radians(r)) for r in rotations)
            avg_rotation = math.degrees(math.atan2(sin_sum / len(rotations), cos_sum / len(rotations)))
        else:
            avg_rotation = 0.0

        consolidated = {
            'position': (avg_x, avg_y),
            'confidence': min(1.0, avg_confidence),
            'detection_method': 'clustering_modifier',
            'original_methods': list(set(j.get('detection_method', 'unknown') for j in cluster)),
            'cluster_size': len(cluster),
            'index': int(round(avg_index)),  # Add the missing index field
            'rotation': avg_rotation  # Add rotation field
        }

        return consolidated





    def run_single_method_detection(self, method_id):
        """Run detection for a single method"""
        import time

        if not self.all_strokes:
            return

        start_time = time.time()
        all_joints = []

        # Get method parameters
        params = {}
        if 'params' in self.method_controls[method_id]:
            for param_id, param_var in self.method_controls[method_id]['params'].items():
                params[param_id] = param_var.get()

        print(f"🔍 Running {method_id} detection with params: {params}")

        # Run detection on each stroke
        for stroke_idx, stroke in enumerate(self.all_strokes):
            stroke_joints = []

            if method_id == 'sequential':
                stroke_joints = self.run_sequential_detection(stroke, params)
            elif method_id == 'angle':
                stroke_joints = self.run_angle_detection(stroke, params)
            elif method_id == 'curvature':
                stroke_joints = self.run_curvature_detection(stroke, params)
            elif method_id == 'velocity':
                stroke_joints = self.run_velocity_detection(stroke, params)
            elif method_id == 'clustering':
                stroke_joints = self.run_clustering_detection(stroke, params)
            elif method_id == 'hybrid':
                stroke_joints = self.run_hybrid_detection(stroke, params)

            # Add stroke index to joints
            for joint in stroke_joints:
                joint['stroke_idx'] = stroke_idx
                joint['method'] = method_id

            all_joints.extend(stroke_joints)

        # Store results
        processing_time = time.time() - start_time
        confidence_scores = [j.get('confidence', 0.5) for j in all_joints]

        self.method_results[method_id] = {
            'joints': all_joints,
            'processing_time': processing_time,
            'confidence_scores': confidence_scores,
            'enabled': self.method_controls[method_id]['enabled'].get()
        }

        print(f"✅ {method_id} detection complete: {len(all_joints)} joints in {processing_time:.3f}s")

    def run_sequential_detection(self, stroke, params):
        """Run sequential analysis detection"""
        if len(stroke) < 5:
            return []

        # Use existing sequential analysis method
        joints = self.sequential_joint_analysis(stroke, params.get('angle_threshold', 30))
        # Filter by confidence threshold
        confidence_threshold = params.get('confidence_threshold', 0.5)
        return [j for j in joints if j.get('confidence', 0.5) >= confidence_threshold]

    def run_angle_detection(self, stroke, params):
        """Run traditional angle-based detection"""
        if len(stroke) < 3:
            return []

        joints = []
        angle_threshold = params.get('angle_threshold', 30)
        adaptive_factor = params.get('adaptive_threshold', 0.5)
        confidence_threshold = params.get('confidence_threshold', 0.4)

        for i in range(1, len(stroke) - 1):
            p1, p2, p3 = stroke[i-1], stroke[i], stroke[i+1]

            # Calculate vectors
            v1 = (p2[0] - p1[0], p2[1] - p1[1])
            v2 = (p3[0] - p2[0], p3[1] - p2[1])

            # Calculate angle
            angle = self.calculate_angle_between_vectors(v1, v2)

            # Adaptive threshold based on vector lengths
            v1_len = math.sqrt(v1[0]**2 + v1[1]**2)
            v2_len = math.sqrt(v2[0]**2 + v2[1]**2)
            min_length = min(v1_len, v2_len)

            adaptive_threshold = angle_threshold * (1.0 - min(min_length / 50.0, adaptive_factor))

            if abs(angle) > adaptive_threshold:
                confidence = min(1.0, abs(angle) / 90.0)  # Normalize confidence

                if confidence >= confidence_threshold:
                    joints.append({
                        'position': p2,
                        'angle': angle,
                        'index': i,
                        'confidence': confidence,
                        'detection_method': 'angle'
                    })

        return joints

    def run_curvature_detection(self, stroke, params):
        """Run curvature-based detection"""
        if len(stroke) < 5:
            return []

        joints = []
        curvature_sensitivity = params.get('curvature_sensitivity', 0.01)
        peak_threshold = params.get('peak_threshold', 0.5)
        confidence_threshold = params.get('confidence_threshold', 0.3)

        # Calculate curvature at each point
        curvatures = []
        for i in range(2, len(stroke) - 2):
            # Use 5-point window for curvature calculation
            p1, p2, p3, p4, p5 = stroke[i-2:i+3]

            # Calculate vectors
            v1 = (p3[0] - p1[0], p3[1] - p1[1])
            v2 = (p5[0] - p3[0], p5[1] - p3[1])

            # Calculate curvature using cross product
            if v1[0] != 0 or v1[1] != 0 and v2[0] != 0 or v2[1] != 0:
                cross_product = abs(v1[0] * v2[1] - v1[1] * v2[0])
                v1_len = math.sqrt(v1[0]**2 + v1[1]**2)
                v2_len = math.sqrt(v2[0]**2 + v2[1]**2)

                if v1_len > 0 and v2_len > 0:
                    curvature = cross_product / (v1_len * v2_len)
                    curvatures.append((i, curvature))

        # Find curvature peaks
        for i, (point_idx, curvature) in enumerate(curvatures):
            if curvature > curvature_sensitivity:
                # Check if it's a local maximum
                is_peak = True
                for j in range(max(0, i-2), min(len(curvatures), i+3)):
                    if j != i and curvatures[j][1] > curvature * peak_threshold:
                        is_peak = False
                        break

                if is_peak:
                    confidence = min(1.0, curvature / (curvature_sensitivity * 10))

                    if confidence >= confidence_threshold:
                        joints.append({
                            'position': stroke[point_idx],
                            'curvature': curvature,
                            'index': point_idx,
                            'confidence': confidence,
                            'detection_method': 'curvature'
                        })

        return joints

    def run_velocity_detection(self, stroke, params):
        """Run velocity-based pause detection"""
        if len(stroke) < 5:
            return []

        joints = []
        velocity_threshold = params.get('velocity_threshold', 0.3)
        smoothing_window = int(params.get('smoothing_window', 3))
        confidence_threshold = params.get('confidence_threshold', 0.4)

        # Calculate velocities
        velocities = []
        for i in range(1, len(stroke)):
            dx = stroke[i][0] - stroke[i-1][0]
            dy = stroke[i][1] - stroke[i-1][1]
            velocity = math.sqrt(dx*dx + dy*dy)
            velocities.append(velocity)

        # Smooth velocities
        if len(velocities) >= smoothing_window:
            smoothed_velocities = []
            half_window = smoothing_window // 2

            for i in range(len(velocities)):
                start_idx = max(0, i - half_window)
                end_idx = min(len(velocities), i + half_window + 1)
                avg_velocity = sum(velocities[start_idx:end_idx]) / (end_idx - start_idx)
                smoothed_velocities.append(avg_velocity)

            # Find velocity minima (pause points)
            max_velocity = max(smoothed_velocities) if smoothed_velocities else 1.0
            threshold = max_velocity * velocity_threshold

            for i in range(1, len(smoothed_velocities) - 1):
                if (smoothed_velocities[i] < threshold and
                    smoothed_velocities[i] < smoothed_velocities[i-1] and
                    smoothed_velocities[i] < smoothed_velocities[i+1]):

                    # Calculate confidence based on how much slower this point is
                    relative_slowness = 1.0 - (smoothed_velocities[i] / max_velocity)
                    confidence = min(1.0, relative_slowness * 2.0)

                    if confidence >= confidence_threshold:
                        joints.append({
                            'position': stroke[i+1],  # +1 because velocities array is offset
                            'velocity': smoothed_velocities[i],
                            'index': i+1,
                            'confidence': confidence,
                            'detection_method': 'velocity'
                        })

        return joints

    def run_clustering_detection(self, stroke, params):
        """Run distance-based clustering detection"""
        if len(stroke) < 5:
            return []

        cluster_radius = params.get('cluster_radius', 20)
        min_cluster_size = int(params.get('min_cluster_size', 2))
        confidence_threshold = params.get('confidence_threshold', 0.5)

        # Get candidate points from other methods with relaxed thresholds
        angle_candidates = self.run_angle_detection(stroke, {'angle_threshold': 20, 'confidence_threshold': 0.2})
        curvature_candidates = self.run_curvature_detection(stroke, {'curvature_sensitivity': 0.005, 'confidence_threshold': 0.2})
        velocity_candidates = self.run_velocity_detection(stroke, {'velocity_threshold': 0.5, 'confidence_threshold': 0.2})

        # Combine all candidates
        all_candidates = angle_candidates + curvature_candidates + velocity_candidates

        if len(all_candidates) < min_cluster_size:
            return []

        # Cluster nearby candidates
        clusters = []
        used_candidates = set()

        for i, candidate in enumerate(all_candidates):
            if i in used_candidates:
                continue

            cluster = [candidate]
            used_candidates.add(i)

            # Find nearby candidates
            for j, other_candidate in enumerate(all_candidates):
                if j in used_candidates or j == i:
                    continue

                distance = math.sqrt(
                    (candidate['position'][0] - other_candidate['position'][0])**2 +
                    (candidate['position'][1] - other_candidate['position'][1])**2
                )

                if distance <= cluster_radius:
                    cluster.append(other_candidate)
                    used_candidates.add(j)

            if len(cluster) >= min_cluster_size:
                clusters.append(cluster)

        # Create joint from each cluster
        joints = []
        for cluster in clusters:
            # Calculate cluster center (weighted by confidence)
            total_weight = sum(c.get('confidence', 0.5) for c in cluster)
            if total_weight == 0:
                continue

            avg_x = sum(c['position'][0] * c.get('confidence', 0.5) for c in cluster) / total_weight
            avg_y = sum(c['position'][1] * c.get('confidence', 0.5) for c in cluster) / total_weight

            # Calculate combined confidence
            confidence = min(1.0, total_weight / len(cluster))

            if confidence >= confidence_threshold:
                # Find closest original point
                center = (avg_x, avg_y)
                closest_idx = 0
                min_dist = float('inf')

                for idx, point in enumerate(stroke):
                    dist = math.sqrt((point[0] - center[0])**2 + (point[1] - center[1])**2)
                    if dist < min_dist:
                        min_dist = dist
                        closest_idx = idx

                joints.append({
                    'position': stroke[closest_idx],
                    'cluster_size': len(cluster),
                    'index': closest_idx,
                    'confidence': confidence,
                    'detection_method': 'clustering'
                })

        return joints

    def run_hybrid_detection(self, stroke, params):
        """Run hybrid detection combining multiple methods"""
        if len(stroke) < 5:
            return []

        # Get results from individual methods
        angle_joints = self.run_angle_detection(stroke, {'angle_threshold': 25, 'confidence_threshold': 0.2})
        curvature_joints = self.run_curvature_detection(stroke, {'curvature_sensitivity': 0.008, 'confidence_threshold': 0.2})
        velocity_joints = self.run_velocity_detection(stroke, {'velocity_threshold': 0.4, 'confidence_threshold': 0.2})

        # Get weights
        weight_angle = params.get('weight_angle', 0.4)
        weight_curvature = params.get('weight_curvature', 0.3)
        weight_velocity = params.get('weight_velocity', 0.3)
        confidence_threshold = params.get('confidence_threshold', 0.6)

        # Combine results with proximity clustering
        all_joints = []
        for joint in angle_joints:
            joint['method_weight'] = weight_angle
            all_joints.append(joint)
        for joint in curvature_joints:
            joint['method_weight'] = weight_curvature
            all_joints.append(joint)
        for joint in velocity_joints:
            joint['method_weight'] = weight_velocity
            all_joints.append(joint)

        if not all_joints:
            return []

        # Cluster nearby joints
        cluster_radius = 15  # pixels
        clusters = []
        used_joints = set()

        for i, joint in enumerate(all_joints):
            if i in used_joints:
                continue

            cluster = [(joint, joint['detection_method'], joint.get('confidence', 0.5) * joint['method_weight'])]
            used_joints.add(i)

            # Find nearby joints
            for j, other_joint in enumerate(all_joints):
                if j in used_joints or j == i:
                    continue

                distance = math.sqrt(
                    (joint['position'][0] - other_joint['position'][0])**2 +
                    (joint['position'][1] - other_joint['position'][1])**2
                )

                if distance <= cluster_radius:
                    weighted_conf = other_joint.get('confidence', 0.5) * other_joint['method_weight']
                    cluster.append((other_joint, other_joint['detection_method'], weighted_conf))
                    used_joints.add(j)

            clusters.append(cluster)

        # Create final joints from clusters
        final_joints = []
        for cluster in clusters:
            if len(cluster) == 0:
                continue

            # Calculate weighted average position
            total_weight = sum(conf for _, _, conf in cluster)
            if total_weight == 0:
                continue

            avg_x = sum(cand['position'][0] * conf for cand, _, conf in cluster) / total_weight
            avg_y = sum(cand['position'][1] * conf for cand, _, conf in cluster) / total_weight

            # Find best angle and method
            best_candidate, best_method, best_confidence = max(cluster, key=lambda x: x[2])

            # Combine detection methods
            methods = list(set(method for _, method, _ in cluster))
            combined_confidence = min(1.0, total_weight)

            if combined_confidence >= confidence_threshold:
                # Find closest original point
                center = (avg_x, avg_y)
                closest_idx = 0
                min_dist = float('inf')

                for idx, point in enumerate(stroke):
                    dist = math.sqrt((point[0] - center[0])**2 + (point[1] - center[1])**2)
                    if dist < min_dist:
                        min_dist = dist
                        closest_idx = idx

                final_joints.append({
                    'position': stroke[closest_idx],
                    'combined_methods': methods,
                    'index': closest_idx,
                    'confidence': combined_confidence,
                    'detection_method': 'hybrid'
                })

        return final_joints

    def update_comparison_visualization(self):
        """Update the comparison visualization canvas - refactored for pipeline"""
        if not hasattr(self, 'comparison_canvas'):
            return

        # Clear canvas
        self.comparison_canvas.delete("all")

        if not self.all_strokes:
            return

        # Calculate canvas bounds
        all_points = [point for stroke in self.all_strokes for point in stroke]
        if not all_points:
            return

        min_x = min(p[0] for p in all_points) - 50
        max_x = max(p[0] for p in all_points) + 50
        min_y = min(p[1] for p in all_points) - 50
        max_y = max(p[1] for p in all_points) + 50

        # Set scroll region
        self.comparison_canvas.configure(scrollregion=(min_x, min_y, max_x, max_y))

        # Draw original strokes
        for stroke_idx, stroke in enumerate(self.all_strokes):
            for i in range(len(stroke) - 1):
                self.comparison_canvas.create_line(
                    stroke[i][0], stroke[i][1],
                    stroke[i+1][0], stroke[i+1][1],
                    fill="#CCCCCC", width=2, tags="stroke"
                )

        # Draw pipeline results
        display_mode = self.comparison_mode.get()

        if display_mode == "overlay":
            self.draw_pipeline_overlay()
        elif display_mode == "side_by_side":
            self.draw_pipeline_side_by_side()
        elif display_mode == "individual":
            self.draw_pipeline_individual()

        # Update metrics display
        if self.show_metrics.get():
            self.update_pipeline_metrics_display()

    def draw_pipeline_overlay(self):
        """Draw pipeline results overlaid: final filtered results"""
        # Draw final pipeline results (after filtering)
        if hasattr(self, 'final_pipeline_results') and self.final_pipeline_results:
            final_results = self.final_pipeline_results
            base_method = final_results['base_method']
            base_color = self.base_method_colors.get(base_method, "#000000")

            # Draw final filtered joints
            for joint in final_results['joints']:
                x, y = joint['position']
                confidence = joint.get('confidence', 0.5)
                detection_method = joint.get('detection_method', base_method)

                # Draw joint (circle with method-specific color)
                method_color = self.base_method_colors.get(detection_method, base_color)
                size = 4 + int(confidence * 4)
                self.comparison_canvas.create_oval(
                    x - size, y - size, x + size, y + size,
                    fill=method_color, outline=method_color, width=2,
                    tags=f"final_joint_{detection_method}"
                )

                # Label for high confidence
                if confidence > 0.7:
                    self.comparison_canvas.create_text(
                        x - size - 15, y,
                        text=f"F:{confidence:.2f}",
                        fill=method_color, font=("Arial", 8),
                        tags=f"final_confidence_{detection_method}"
                    )

            # Add filtering info text
            info_text = f"Final Results: {len(final_results['joints'])} joints"
            if final_results['raw_joint_count'] != len(final_results['joints']):
                info_text += f" (filtered from {final_results['raw_joint_count']})"

            self.comparison_canvas.create_text(
                10, 10, text=info_text, fill="#000000", font=("Arial", 10, "bold"),
                anchor="nw", tags="filter_info"
            )
        else:
            # Fallback: show message if no final results available
            self.comparison_canvas.create_text(
                200, 100, text="No pipeline results available. Click 'Run Full Comparison' to generate results.",
                fill="#666666", font=("Arial", 12), anchor="center", tags="no_results"
            )

    def draw_pipeline_side_by_side(self):
        """Draw pipeline results side by side (simplified - use overlay for now)"""
        self.draw_pipeline_overlay()

    def draw_pipeline_individual(self):
        """Draw individual pipeline stage results (simplified - use overlay for now)"""
        self.draw_pipeline_overlay()

    def update_pipeline_metrics_display(self):
        """Update the performance metrics display for pipeline"""
        if not hasattr(self, 'metrics_frame'):
            return

        # Clear previous metrics
        for widget in self.metrics_frame.winfo_children():
            widget.destroy()

        # Create metrics display
        metrics_text = "Pipeline Performance Metrics:\n\n"

        # Base method metrics
        base_method = self.selected_base_method.get()
        if hasattr(self, 'base_method_results') and base_method in self.base_method_results:
            base_results = self.base_method_results[base_method]
            joint_count = len(base_results['joints'])
            processing_time = base_results['processing_time']
            avg_confidence = sum(base_results['confidence_scores']) / len(base_results['confidence_scores']) if base_results['confidence_scores'] else 0

            metrics_text += f"Base Method ({base_method}):\n"
            metrics_text += f"  • Joints detected: {joint_count}\n"
            metrics_text += f"  • Processing time: {processing_time:.3f}s\n"
            metrics_text += f"  • Avg confidence: {avg_confidence:.2f}\n\n"

        # Modifier metrics
        if hasattr(self, 'modifier_results'):
            total_modifier_time = 0
            for modifier_id, modifier_results in self.modifier_results.items():
                if modifier_results['enabled']:
                    input_joints = modifier_results['input_joints']
                    output_joints = modifier_results['output_joints']
                    processing_time = modifier_results['processing_time']
                    total_modifier_time += processing_time

                    metrics_text += f"Modifier ({modifier_id}):\n"
                    metrics_text += f"  • Input joints: {input_joints}\n"
                    metrics_text += f"  • Output joints: {output_joints}\n"
                    metrics_text += f"  • Processing time: {processing_time:.3f}s\n"

                    if modifier_id == 'clustering':
                        reduction = ((input_joints - output_joints) / input_joints * 100) if input_joints > 0 else 0
                        metrics_text += f"  • Joint reduction: {reduction:.1f}%\n"

                    metrics_text += "\n"

        # Final filtered results (what "Apply to Main" will use)
        if hasattr(self, 'final_pipeline_results') and self.final_pipeline_results:
            final_results = self.final_pipeline_results
            total_time = 0
            if hasattr(self, 'base_method_results') and base_method in self.base_method_results:
                total_time += self.base_method_results[base_method]['processing_time']
            if hasattr(self, 'modifier_results'):
                total_time += sum(r['processing_time'] for r in self.modifier_results.values() if r['enabled'])

            metrics_text += f"Final Results (After Filtering):\n"
            metrics_text += f"  • Raw pipeline joints: {final_results['raw_joint_count']}\n"
            metrics_text += f"  • Confidence threshold: ≥{final_results['confidence_threshold']:.1f}\n"

            if final_results['joint_limiting_enabled']:
                metrics_text += f"  • Joint limiting: ON (max {final_results['max_joints_per_stroke']} per stroke)\n"
            else:
                metrics_text += f"  • Joint limiting: OFF\n"

            metrics_text += f"  • Final joints: {len(final_results['joints'])}\n"
            metrics_text += f"  • Total pipeline time: {total_time:.3f}s\n\n"

            if final_results['raw_joint_count'] != len(final_results['joints']):
                reduction = ((final_results['raw_joint_count'] - len(final_results['joints'])) / final_results['raw_joint_count'] * 100)
                metrics_text += f"  • Filtering reduction: {reduction:.1f}%\n"

            metrics_text += f"\n⚠️ This preview shows exactly what 'Apply to Main' will produce."

        metrics_label = ttk.Label(self.metrics_frame, text=metrics_text, font=("Courier", 9), justify="left")
        metrics_label.pack(anchor="w")

    def draw_overlay_joints(self):
        """Draw all method results overlaid on the same canvas"""
        for method_id, results in self.method_results.items():
            if not results['enabled'] or method_id not in self.method_controls:
                continue

            if not self.method_controls[method_id]['enabled'].get():
                continue

            color = self.method_colors.get(method_id, "#000000")
            opacity = self.method_controls[method_id]['opacity'].get()

            # Convert opacity to color (simplified)
            if opacity < 1.0:
                # Create lighter version of color for transparency effect
                color = self.lighten_color(color, 1.0 - opacity)

            for joint in results['joints']:
                x, y = joint['position']
                confidence = joint.get('confidence', 0.5)

                # Draw joint marker
                size = 3 + int(confidence * 5)  # Size based on confidence
                self.comparison_canvas.create_oval(
                    x - size, y - size, x + size, y + size,
                    fill=color, outline=color, width=2,
                    tags=f"joint_{method_id}"
                )

                # Draw confidence and rotation text
                if confidence > 0.7:  # Only show for high confidence
                    rotation = joint.get('rotation', 0)
                    label_text = f"{confidence:.2f}\n{rotation:.0f}°"
                    self.comparison_canvas.create_text(
                        x + size + 5, y,
                        text=label_text,
                        fill=color, font=("Arial", 8),
                        tags=f"confidence_{method_id}"
                    )

    def draw_side_by_side_joints(self):
        """Draw methods side by side (simplified implementation)"""
        # For now, just draw overlay - full side-by-side would require complex layout
        self.draw_overlay_joints()

    def draw_individual_joints(self):
        """Draw individual method results (simplified implementation)"""
        # For now, just draw overlay - full individual would require method selection
        self.draw_overlay_joints()

    def lighten_color(self, color, factor):
        """Lighten a hex color by a factor (0.0 to 1.0)"""
        try:
            # Remove # if present
            color = color.lstrip('#')

            # Convert to RGB
            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)

            # Lighten
            r = int(r + (255 - r) * factor)
            g = int(g + (255 - g) * factor)
            b = int(b + (255 - b) * factor)

            # Convert back to hex
            return f"#{r:02x}{g:02x}{b:02x}"
        except:
            return color

    def update_metrics_display(self):
        """Update the performance metrics display"""
        # Clear previous metrics
        for widget in self.metrics_frame.winfo_children():
            widget.destroy()

        # Create metrics display
        metrics_text = "Performance Metrics:\n"
        total_joints = 0

        for method_id, results in self.method_results.items():
            if results['enabled'] and method_id in self.method_controls:
                if self.method_controls[method_id]['enabled'].get():
                    joint_count = len(results['joints'])
                    processing_time = results['processing_time']
                    avg_confidence = sum(results['confidence_scores']) / len(results['confidence_scores']) if results['confidence_scores'] else 0

                    metrics_text += f"{method_id}: {joint_count} joints, {processing_time:.3f}s, avg conf: {avg_confidence:.2f}\n"
                    total_joints += joint_count

        metrics_text += f"\nTotal joints detected: {total_joints}"

        metrics_label = ttk.Label(self.metrics_frame, text=metrics_text, font=("Courier", 9))
        metrics_label.pack(anchor="w")

    def on_comparison_canvas_click(self, event):
        """Handle clicks on the comparison canvas"""
        # Convert canvas coordinates
        canvas_x = self.comparison_canvas.canvasx(event.x)
        canvas_y = self.comparison_canvas.canvasy(event.y)

        # Find nearby joints and show details
        for method_id, results in self.method_results.items():
            if not results['enabled']:
                continue

            for joint in results['joints']:
                jx, jy = joint['position']
                distance = math.sqrt((canvas_x - jx)**2 + (canvas_y - jy)**2)

                if distance <= 10:  # Within 10 pixels
                    self.show_joint_details(joint, method_id)
                    return

    def on_comparison_canvas_motion(self, event):
        """Handle mouse motion on comparison canvas"""
        # Could implement hover effects here
        pass

    def show_joint_details(self, joint, method_id):
        """Show detailed information about a joint"""
        details = f"Method: {method_id}\n"
        details += f"Position: ({joint['position'][0]:.1f}, {joint['position'][1]:.1f})\n"
        details += f"Confidence: {joint.get('confidence', 0.5):.3f}\n"
        details += f"Index: {joint.get('index', 'N/A')}\n"

        if 'angle' in joint:
            details += f"Angle: {joint['angle']:.1f}°\n"
        if 'curvature' in joint:
            details += f"Curvature: {joint['curvature']:.4f}\n"
        if 'velocity' in joint:
            details += f"Velocity: {joint['velocity']:.2f}\n"

        tk.messagebox.showinfo("Joint Details", details)

    def export_comparison_settings(self):
        """Export current pipeline comparison settings to file"""
        try:
            settings = {
                'pipeline_version': '2.0',
                'selected_base_method': self.selected_base_method.get(),
                'base_methods': {},
                'modifiers': {},
                'display_mode': self.comparison_mode.get(),
                'show_metrics': self.show_metrics.get()
            }

            # Export base method parameters
            for method_id, controls in self.base_method_controls.items():
                settings['base_methods'][method_id] = {
                    'params': {}
                }
                if 'params' in controls:
                    for param_id, param_var in controls['params'].items():
                        settings['base_methods'][method_id]['params'][param_id] = param_var.get()

            # Export modifier settings
            for modifier_id, controls in self.modifier_controls.items():
                settings['modifiers'][modifier_id] = {
                    'enabled': self.enabled_modifiers[modifier_id].get() if modifier_id in self.enabled_modifiers else False,
                    'params': {}
                }
                if 'params' in controls:
                    for param_id, param_var in controls['params'].items():
                        settings['modifiers'][modifier_id]['params'][param_id] = param_var.get()

            # Save to file
            filename = tk.filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'w') as f:
                    json.dump(settings, f, indent=2)
                tk.messagebox.showinfo("Export Complete", f"Pipeline settings exported to {filename}")

        except Exception as e:
            tk.messagebox.showerror("Export Error", f"Failed to export pipeline settings: {str(e)}")

    def import_comparison_settings(self):
        """Import pipeline comparison settings from file"""
        try:
            filename = tk.filedialog.askopenfilename(
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'r') as f:
                    settings = json.load(f)

                # Check if this is a pipeline settings file
                if 'pipeline_version' in settings:
                    # Import pipeline settings (v2.0)

                    # Set selected base method
                    if 'selected_base_method' in settings:
                        self.selected_base_method.set(settings['selected_base_method'])

                    # Import base method parameters
                    if 'base_methods' in settings:
                        for method_id, method_settings in settings['base_methods'].items():
                            if method_id in self.base_method_controls and 'params' in method_settings:
                                for param_id, param_value in method_settings['params'].items():
                                    if param_id in self.base_method_controls[method_id]['params']:
                                        self.base_method_controls[method_id]['params'][param_id].set(param_value)

                    # Import modifier settings
                    if 'modifiers' in settings:
                        for modifier_id, modifier_settings in settings['modifiers'].items():
                            if modifier_id in self.enabled_modifiers:
                                # Set enabled state
                                if 'enabled' in modifier_settings:
                                    self.enabled_modifiers[modifier_id].set(modifier_settings['enabled'])

                                # Set parameters
                                if 'params' in modifier_settings and modifier_id in self.modifier_controls:
                                    for param_id, param_value in modifier_settings['params'].items():
                                        if param_id in self.modifier_controls[modifier_id]['params']:
                                            self.modifier_controls[modifier_id]['params'][param_id].set(param_value)

                    # Set display options
                    if 'display_mode' in settings:
                        self.comparison_mode.set(settings['display_mode'])
                    if 'show_metrics' in settings:
                        self.show_metrics.set(settings['show_metrics'])

                    # Update visualization
                    self.run_pipeline_comparison()
                    tk.messagebox.showinfo("Import Complete", f"Pipeline settings imported from {filename}")

                else:
                    # Legacy settings format - show warning
                    tk.messagebox.showwarning("Legacy Format",
                        "This appears to be a legacy settings file from before the pipeline architecture. "
                        "Please export new settings to use the current pipeline format.")

        except Exception as e:
            tk.messagebox.showerror("Import Error", f"Failed to import pipeline settings: {str(e)}")

    def apply_pipeline_to_main(self):
        """Apply current pipeline configuration to main analyzer window"""
        try:
            print("🔄 Applying pipeline configuration to main analyzer...")

            # Get current pipeline configuration
            selected_base_method = self.selected_base_method.get()

            # Store the pipeline configuration in main analyzer
            self.main_pipeline_config = {
                'base_method': selected_base_method,
                'base_method_params': {},
                'enabled_modifiers': {},
                'modifier_params': {}
            }

            # Copy base method parameters
            if selected_base_method in self.base_method_controls and 'params' in self.base_method_controls[selected_base_method]:
                for param_id, param_var in self.base_method_controls[selected_base_method]['params'].items():
                    self.main_pipeline_config['base_method_params'][param_id] = param_var.get()

            # Copy modifier settings
            for modifier_id, enabled_var in self.enabled_modifiers.items():
                self.main_pipeline_config['enabled_modifiers'][modifier_id] = enabled_var.get()

                # Copy modifier parameters if enabled
                if enabled_var.get() and modifier_id in self.modifier_controls and 'params' in self.modifier_controls[modifier_id]:
                    self.main_pipeline_config['modifier_params'][modifier_id] = {}
                    for param_id, param_var in self.modifier_controls[modifier_id]['params'].items():
                        self.main_pipeline_config['modifier_params'][modifier_id][param_id] = param_var.get()

            print(f"📋 Pipeline config: {selected_base_method} base method")
            print(f"📋 Enabled modifiers: {[mod for mod, enabled in self.main_pipeline_config['enabled_modifiers'].items() if enabled]}")

            # Re-analyze existing strokes with new pipeline
            if self.all_strokes:
                print(f"🔄 Re-analyzing {len(self.all_strokes)} existing strokes with new pipeline...")
                self.analyze_strokes()
                self.generate_skeleton()
                self.redraw_all()

                success_msg = (f"✅ Pipeline configuration applied to main analyzer!\n\n"
                             f"Base Method: {selected_base_method}\n"
                             f"Enabled Modifiers: {', '.join([mod for mod, enabled in self.main_pipeline_config['enabled_modifiers'].items() if enabled]) or 'None'}\n"
                             f"Re-analyzed {len(self.all_strokes)} strokes")
            else:
                success_msg = (f"✅ Pipeline configuration applied to main analyzer!\n\n"
                             f"Base Method: {selected_base_method}\n"
                             f"Enabled Modifiers: {', '.join([mod for mod, enabled in self.main_pipeline_config['enabled_modifiers'].items() if enabled]) or 'None'}\n"
                             f"Configuration will be used for future stroke analysis")

            tk.messagebox.showinfo("Pipeline Applied", success_msg)

        except Exception as e:
            tk.messagebox.showerror("Apply Error", f"Failed to apply pipeline to main analyzer: {str(e)}")
            print(f"❌ Error applying pipeline: {e}")


# Main execution
if __name__ == "__main__":
    print("🎨 Stick Figure Analyzer - Starting Application")
    app = StickFigureAnalyzer()
    app.run()
